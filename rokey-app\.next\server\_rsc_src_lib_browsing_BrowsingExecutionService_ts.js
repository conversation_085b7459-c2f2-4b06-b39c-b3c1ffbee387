"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        const keysString = process.env.BROWSERLESS_API_KEYS;\n        if (!keysString) {\n            console.error('BROWSERLESS_API_KEYS not found in environment variables');\n            return;\n        }\n        this.apiKeys = keysString.split(',').map((key)=>key.trim()).filter(Boolean);\n        console.log(`Initialized Browserless service with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n          return extractedResults;\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n/* harmony import */ var _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SmartBrowsingExecutor */ \"(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n        this.smartBrowsingExecutor = _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__.SmartBrowsingExecutor.getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   * Now uses SmartBrowsingExecutor for intelligent, plan-based browsing\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery, useSmartBrowsing = true) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            console.log(`[Browsing Execution] Starting ${useSmartBrowsing ? 'SMART' : 'SIMPLE'} browsing`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            // Use Smart Browsing for complex tasks\n            if (useSmartBrowsing) {\n                console.log(`[Browsing Execution] 🧠 Using Smart Browsing Executor`);\n                const smartResult = await this.smartBrowsingExecutor.executeSmartBrowsing(refinedQuery || query, browsingConfig, browsingType);\n                if (smartResult.success) {\n                    return {\n                        success: true,\n                        content: smartResult.content,\n                        modelUsed: browsingConfig.browsing_models[0]?.model || 'smart-browsing',\n                        providerUsed: browsingConfig.browsing_models[0]?.provider || 'smart-browsing',\n                        browsingData: smartResult.plan\n                    };\n                } else {\n                    console.log(`[Browsing Execution] Smart browsing failed, falling back to simple browsing: ${smartResult.error}`);\n                // Fall back to simple browsing\n                }\n            }\n            // Fallback to simple browsing (original logic)\n            console.log(`[Browsing Execution] 🔄 Using Simple Browsing (fallback)`);\n            return await this.executeSimpleBrowsing(query, browsingConfig, browsingType, refinedQuery);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Execute simple browsing (original logic) as fallback\n   */ async executeSimpleBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        // Sort models by order for fallback\n        const sortedModels = [\n            ...browsingConfig.browsing_models\n        ].sort((a, b)=>a.order - b.order);\n        console.log(`[Simple Browsing] Starting with ${sortedModels.length} models configured`);\n        let lastError = null;\n        // Try each model in order (strict fallback pattern)\n        for (const model of sortedModels){\n            try {\n                console.log(`[Simple Browsing] Attempting with ${model.provider}/${model.model}`);\n                // First, perform the web browsing\n                const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                if (!browsingResult.success) {\n                    throw new Error(browsingResult.error || 'Browsing failed');\n                }\n                console.log(`[Simple Browsing] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);\n                // Then, use the AI model to process the browsing results\n                const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                if (aiResult.success) {\n                    console.log(`[Simple Browsing] ✅ Success with ${model.provider}/${model.model}`);\n                    return {\n                        success: true,\n                        content: aiResult.content,\n                        modelUsed: model.model,\n                        providerUsed: model.provider,\n                        browsingData: browsingResult.data\n                    };\n                } else {\n                    throw new Error(aiResult.error || 'AI processing failed');\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                lastError = errorMessage;\n                console.log(`[Simple Browsing] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                continue;\n            }\n        }\n        // If we get here, all models failed\n        return {\n            success: false,\n            error: `All browsing models failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtract(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtract(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        return `You are an AI assistant that processes web browsing results to answer user queries.\n\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\nWEB BROWSING RESULTS:\n${dataStr}\n\nINSTRUCTIONS:\n1. Analyze the browsing results carefully\n2. Extract relevant information that answers the user's query\n3. Provide a comprehensive, well-structured response\n4. If the browsing results don't contain relevant information, say so clearly\n5. Include specific details, numbers, dates, and facts from the browsing results\n6. Organize the information in a clear, readable format\n7. Cite sources when possible (URLs, website names, etc.)\n\nPlease provide a helpful response based on the browsing results:`;\n    }\n    /**\n   * Get the correct model ID for API calls (following RouKey's pattern)\n   * OpenRouter keeps full model ID, other providers strip the prefix\n   */ getEffectiveModelId(model) {\n        // For OpenRouter, return the full model ID\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        // For other providers, extract the model name after the prefix\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            // Get the effective model ID following RouKey's pattern\n            const effectiveModelId = this.getEffectiveModelId(model);\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            console.log(`[Browsing AI] Calling ${model.provider} API with model ${effectiveModelId} (original: ${model.model})...`);\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)\n                content = result.choices?.[0]?.message?.content;\n            }\n            console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);\n            if (!content || content.trim().length === 0) {\n                console.error(`[Browsing AI] No content extracted from response. Full response:`, result);\n                return {\n                    error: 'No content returned from AI model - empty response'\n                };\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts":
/*!***************************************************!*\
  !*** ./src/lib/browsing/SmartBrowsingExecutor.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartBrowsingExecutor: () => (/* binding */ SmartBrowsingExecutor)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Smart Browsing Executor - Intelligent plan-based browsing with todo list management\n// Handles complex browsing tasks by creating plans, executing subtasks, and updating progress\n\nclass SmartBrowsingExecutor {\n    constructor(){\n        this.activePlans = new Map();\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!SmartBrowsingExecutor.instance) {\n            SmartBrowsingExecutor.instance = new SmartBrowsingExecutor();\n        }\n        return SmartBrowsingExecutor.instance;\n    }\n    /**\n   * Execute smart browsing with planning and todo list management\n   */ async executeSmartBrowsing(query, browsingConfig, browsingType = 'search') {\n        try {\n            console.log(`[Smart Browsing] 🎯 Starting intelligent browsing for: \"${query}\"`);\n            // Step 1: Create a browsing plan\n            const plan = await this.createBrowsingPlan(query, browsingType, browsingConfig);\n            this.activePlans.set(plan.id, plan);\n            console.log(`[Smart Browsing] 📋 Created plan with ${plan.subtasks.length} subtasks`);\n            this.logPlan(plan);\n            // Step 2: Execute the plan\n            const result = await this.executePlan(plan, browsingConfig);\n            if (result.success) {\n                console.log(`[Smart Browsing] ✅ Plan completed successfully`);\n                return {\n                    success: true,\n                    content: result.content,\n                    plan: plan\n                };\n            } else {\n                console.log(`[Smart Browsing] ❌ Plan failed: ${result.error}`);\n                return {\n                    success: false,\n                    error: result.error,\n                    plan: plan\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Smart browsing failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Create an intelligent browsing plan based on the query\n   */ async createBrowsingPlan(query, browsingType, browsingConfig) {\n        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Use AI to create a smart plan\n        const planningResult = await this.generatePlanWithAI(query, browsingType, browsingConfig);\n        const plan = {\n            id: planId,\n            originalQuery: query,\n            goal: planningResult.goal || `Find comprehensive information about: ${query}`,\n            subtasks: planningResult.subtasks || this.createFallbackPlan(query, browsingType),\n            status: 'planning',\n            progress: 0,\n            gatheredData: {},\n            visitedUrls: [],\n            searchQueries: [],\n            completedSubtasks: [],\n            failedSubtasks: [],\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        return plan;\n    }\n    /**\n   * Generate a browsing plan using AI\n   */ async generatePlanWithAI(query, browsingType, browsingConfig) {\n        try {\n            const model = browsingConfig.browsing_models[0]; // Use first available model for planning\n            if (!model) {\n                throw new Error('No browsing models available for planning');\n            }\n            const planningPrompt = this.buildPlanningPrompt(query, browsingType);\n            const aiResult = await this.callAIForPlanning(planningPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return this.parsePlanFromAI(aiResult.content, query);\n            } else {\n                console.warn('[Smart Browsing] AI planning failed, using fallback plan');\n                return {\n                    goal: `Find information about: ${query}`,\n                    subtasks: this.createFallbackPlan(query, browsingType)\n                };\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] AI planning error, using fallback:', error);\n            return {\n                goal: `Find information about: ${query}`,\n                subtasks: this.createFallbackPlan(query, browsingType)\n            };\n        }\n    }\n    /**\n   * Build a comprehensive planning prompt for AI\n   */ buildPlanningPrompt(query, browsingType) {\n        return `You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: \"${query}\"\n\nBROWSING TYPE: ${browsingType}\n\nCreate a JSON response with this structure:\n{\n  \"goal\": \"Clear statement of what we want to achieve\",\n  \"subtasks\": [\n    {\n      \"id\": \"unique_id\",\n      \"type\": \"search|navigate|extract|analyze\",\n      \"description\": \"What this subtask does\",\n      \"query\": \"Specific search query or URL\",\n      \"priority\": 1-10,\n      \"searchTerms\": [\"alternative\", \"search\", \"terms\"],\n      \"expectedInfo\": \"What information we expect to find\"\n    }\n  ]\n}\n\nGUIDELINES:\n1. Start with broad searches, then get more specific\n2. Use multiple search strategies and terms\n3. Include fact-checking and verification steps\n4. Plan for 3-7 subtasks maximum\n5. Make search terms diverse and comprehensive\n6. Consider different angles and perspectives\n7. Include analysis steps to synthesize information\n\nCreate a smart, thorough plan:`;\n    }\n    /**\n   * Create a fallback plan when AI planning fails\n   */ createFallbackPlan(query, browsingType) {\n        const baseId = Date.now();\n        return [\n            {\n                id: `search_${baseId}_1`,\n                type: 'search',\n                description: 'Primary search for main topic',\n                query: query,\n                status: 'pending',\n                priority: 10,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: [\n                    query,\n                    `${query} information`,\n                    `${query} details`\n                ],\n                expectedInfo: 'General information about the topic'\n            },\n            {\n                id: `search_${baseId}_2`,\n                type: 'search',\n                description: 'Secondary search for additional details',\n                query: `${query} latest news recent updates`,\n                status: 'pending',\n                priority: 8,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: [\n                    `${query} news`,\n                    `${query} updates`,\n                    `${query} recent`\n                ],\n                expectedInfo: 'Recent developments and news'\n            },\n            {\n                id: `analyze_${baseId}`,\n                type: 'analyze',\n                description: 'Analyze and synthesize gathered information',\n                query: 'synthesize findings',\n                status: 'pending',\n                priority: 5,\n                attempts: 0,\n                maxAttempts: 1,\n                dependencies: [\n                    `search_${baseId}_1`,\n                    `search_${baseId}_2`\n                ],\n                expectedInfo: 'Comprehensive summary of findings'\n            }\n        ];\n    }\n    /**\n   * Execute the browsing plan step by step\n   */ async executePlan(plan, browsingConfig) {\n        plan.status = 'executing';\n        plan.updatedAt = new Date().toISOString();\n        console.log(`[Smart Browsing] 🚀 Starting plan execution`);\n        try {\n            // Execute subtasks in priority order, respecting dependencies\n            while(this.hasRemainingTasks(plan)){\n                const nextTask = this.getNextExecutableTask(plan);\n                if (!nextTask) {\n                    console.log(`[Smart Browsing] ⏸️ No executable tasks remaining, checking if plan is complete`);\n                    break;\n                }\n                console.log(`[Smart Browsing] 🔄 Executing subtask: ${nextTask.description}`);\n                plan.currentSubtask = nextTask.id;\n                nextTask.status = 'in_progress';\n                const taskResult = await this.executeSubtask(nextTask, plan, browsingConfig);\n                if (taskResult.success) {\n                    nextTask.status = 'completed';\n                    nextTask.result = taskResult.data;\n                    plan.completedSubtasks.push(nextTask.id);\n                    console.log(`[Smart Browsing] ✅ Subtask completed: ${nextTask.description}`);\n                } else {\n                    nextTask.attempts++;\n                    nextTask.error = taskResult.error;\n                    if (nextTask.attempts >= nextTask.maxAttempts) {\n                        nextTask.status = 'failed';\n                        plan.failedSubtasks.push(nextTask.id);\n                        console.log(`[Smart Browsing] ❌ Subtask failed permanently: ${nextTask.description}`);\n                    } else {\n                        nextTask.status = 'pending';\n                        console.log(`[Smart Browsing] 🔄 Subtask failed, will retry (${nextTask.attempts}/${nextTask.maxAttempts}): ${nextTask.description}`);\n                    }\n                }\n                // Update progress\n                plan.progress = this.calculateProgress(plan);\n                plan.updatedAt = new Date().toISOString();\n                this.logProgress(plan);\n            }\n            // Generate final result\n            const finalResult = await this.synthesizeFinalResult(plan, browsingConfig);\n            plan.finalResult = finalResult;\n            plan.status = 'completed';\n            plan.progress = 100;\n            return {\n                success: true,\n                content: finalResult\n            };\n        } catch (error) {\n            plan.status = 'failed';\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Plan execution failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Check if there are remaining tasks to execute\n   */ hasRemainingTasks(plan) {\n        return plan.subtasks.some((task)=>task.status === 'pending' || task.status === 'in_progress');\n    }\n    /**\n   * Get the next executable task (highest priority, dependencies met)\n   */ getNextExecutableTask(plan) {\n        const executableTasks = plan.subtasks.filter((task)=>{\n            if (task.status !== 'pending') return false;\n            // Check if dependencies are met\n            if (task.dependencies && task.dependencies.length > 0) {\n                return task.dependencies.every((depId)=>plan.completedSubtasks.includes(depId));\n            }\n            return true;\n        });\n        // Sort by priority (highest first)\n        executableTasks.sort((a, b)=>b.priority - a.priority);\n        return executableTasks[0] || null;\n    }\n    /**\n   * Execute a single subtask\n   */ async executeSubtask(subtask, plan, browsingConfig) {\n        try {\n            switch(subtask.type){\n                case 'search':\n                    return await this.executeSearchSubtask(subtask, plan);\n                case 'navigate':\n                    return await this.executeNavigateSubtask(subtask, plan);\n                case 'extract':\n                    return await this.executeExtractSubtask(subtask, plan);\n                case 'analyze':\n                    return await this.executeAnalyzeSubtask(subtask, plan, browsingConfig);\n                default:\n                    throw new Error(`Unknown subtask type: ${subtask.type}`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Execute a search subtask with smart query refinement\n   */ async executeSearchSubtask(subtask, plan) {\n        const searchTerms = [\n            subtask.query,\n            ...subtask.searchTerms || []\n        ];\n        let lastError = '';\n        // Try different search terms until we get good results\n        for (const searchTerm of searchTerms){\n            try {\n                console.log(`[Smart Browsing] 🔍 Searching for: \"${searchTerm}\"`);\n                plan.searchQueries.push(searchTerm);\n                const result = await this.browserlessService.searchAndExtract(searchTerm);\n                if (result.data && result.data.results && result.data.results.length > 0) {\n                    console.log(`[Smart Browsing] ✅ Found ${result.data.results.length} results for: \"${searchTerm}\"`);\n                    // Store URLs we've found\n                    result.data.results.forEach((item)=>{\n                        if (item.link && !plan.visitedUrls.includes(item.link)) {\n                            plan.visitedUrls.push(item.link);\n                        }\n                    });\n                    return {\n                        success: true,\n                        data: result.data\n                    };\n                } else {\n                    lastError = `No results found for: \"${searchTerm}\"`;\n                    console.log(`[Smart Browsing] ⚠️ ${lastError}`);\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error.message : 'Search failed';\n                console.log(`[Smart Browsing] ❌ Search error for \"${searchTerm}\": ${lastError}`);\n            }\n        }\n        return {\n            success: false,\n            error: `All search terms failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Execute navigate subtask\n   */ async executeNavigateSubtask(subtask, plan) {\n        try {\n            const url = subtask.query;\n            console.log(`[Smart Browsing] 🌐 Navigating to: ${url}`);\n            if (!plan.visitedUrls.includes(url)) {\n                plan.visitedUrls.push(url);\n            }\n            const result = await this.browserlessService.navigateAndExtract(url);\n            if (result.data) {\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data extracted from navigation'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Navigation failed'\n            };\n        }\n    }\n    /**\n   * Execute extract subtask\n   */ async executeExtractSubtask(subtask, plan) {\n        // Similar to navigate but with specific extraction focus\n        return this.executeNavigateSubtask(subtask, plan);\n    }\n    /**\n   * Execute analyze subtask - synthesize gathered information\n   */ async executeAnalyzeSubtask(subtask, plan, browsingConfig) {\n        try {\n            console.log(`[Smart Browsing] 🧠 Analyzing gathered data...`);\n            // Collect all data from completed subtasks\n            const gatheredData = plan.subtasks.filter((task)=>task.status === 'completed' && task.result).map((task)=>({\n                    type: task.type,\n                    description: task.description,\n                    query: task.query,\n                    data: task.result\n                }));\n            if (gatheredData.length === 0) {\n                return {\n                    success: false,\n                    error: 'No data available for analysis'\n                };\n            }\n            // Use AI to analyze and synthesize the data\n            const model = browsingConfig.browsing_models[0];\n            if (!model) {\n                return {\n                    success: false,\n                    error: 'No AI model available for analysis'\n                };\n            }\n            const analysisPrompt = this.buildAnalysisPrompt(plan.originalQuery, gatheredData);\n            const aiResult = await this.callAIForAnalysis(analysisPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return {\n                    success: true,\n                    data: {\n                        analysis: aiResult.content,\n                        sourceData: gatheredData\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    error: aiResult.error || 'Analysis failed'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Analysis failed'\n            };\n        }\n    }\n    /**\n   * Calculate progress percentage\n   */ calculateProgress(plan) {\n        const totalTasks = plan.subtasks.length;\n        const completedTasks = plan.completedSubtasks.length;\n        return Math.round(completedTasks / totalTasks * 100);\n    }\n    /**\n   * Log progress update\n   */ logProgress(plan) {\n        const completed = plan.completedSubtasks.length;\n        const failed = plan.failedSubtasks.length;\n        const total = plan.subtasks.length;\n        const remaining = total - completed - failed;\n        console.log(`[Smart Browsing] 📊 Progress: ${plan.progress}% (${completed}/${total} completed, ${failed} failed, ${remaining} remaining)`);\n    }\n    /**\n   * Build analysis prompt for AI\n   */ buildAnalysisPrompt(originalQuery, gatheredData) {\n        const dataContext = gatheredData.map((item, index)=>`Source ${index + 1} (${item.type}): ${item.description}\\nQuery: ${item.query}\\nData: ${JSON.stringify(item.data, null, 2)}`).join('\\n\\n---\\n\\n');\n        return `You are an expert information analyst. Analyze the following browsing data and provide a comprehensive answer to the original query.\n\nORIGINAL QUERY: \"${originalQuery}\"\n\nGATHERED DATA:\n${dataContext}\n\nPlease provide:\n1. A comprehensive answer to the original query\n2. Key findings and insights\n3. Any conflicting information found\n4. Confidence level in the findings\n5. Recommendations for further research if needed\n\nFormat your response as a clear, well-structured analysis that directly addresses the user's query.`;\n    }\n    /**\n   * Call AI for planning\n   */ async callAIForPlanning(prompt, model) {\n        try {\n            const effectiveModelId = this.getEffectiveModelId(model);\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider (same as BrowsingExecutionService)\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider for planning: ${model.provider}`);\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                content = result.choices?.[0]?.message?.content;\n            }\n            if (!content) {\n                throw new Error('No content returned from AI model');\n            }\n            return {\n                success: true,\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Call AI for analysis (same as planning but different purpose)\n   */ async callAIForAnalysis(prompt, model) {\n        return this.callAIForPlanning(prompt, model); // Same implementation\n    }\n    /**\n   * Get effective model ID (same logic as BrowsingExecutionService)\n   */ getEffectiveModelId(model) {\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Parse plan from AI response\n   */ parsePlanFromAI(aiResponse, originalQuery) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n            if (!jsonMatch) {\n                throw new Error('No JSON found in AI response');\n            }\n            const parsed = JSON.parse(jsonMatch[0]);\n            if (!parsed.goal || !parsed.subtasks || !Array.isArray(parsed.subtasks)) {\n                throw new Error('Invalid plan structure from AI');\n            }\n            // Convert AI subtasks to our format\n            const subtasks = parsed.subtasks.map((task, index)=>({\n                    id: task.id || `ai_task_${Date.now()}_${index}`,\n                    type: task.type || 'search',\n                    description: task.description || `Task ${index + 1}`,\n                    query: task.query || originalQuery,\n                    status: 'pending',\n                    priority: task.priority || 5,\n                    attempts: 0,\n                    maxAttempts: 3,\n                    searchTerms: task.searchTerms || [],\n                    expectedInfo: task.expectedInfo || ''\n                }));\n            return {\n                goal: parsed.goal,\n                subtasks\n            };\n        } catch (error) {\n            console.warn('[Smart Browsing] Failed to parse AI plan, using fallback:', error);\n            return {\n                goal: `Find information about: ${originalQuery}`,\n                subtasks: this.createFallbackPlan(originalQuery, 'search')\n            };\n        }\n    }\n    /**\n   * Synthesize final result from all gathered data\n   */ async synthesizeFinalResult(plan, browsingConfig) {\n        try {\n            // Find the analysis result if available\n            const analysisTask = plan.subtasks.find((task)=>task.type === 'analyze' && task.status === 'completed' && task.result);\n            if (analysisTask && analysisTask.result?.analysis) {\n                return analysisTask.result.analysis;\n            }\n            // If no analysis task, create a summary from all completed tasks\n            const completedTasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n            if (completedTasks.length === 0) {\n                return `No information was successfully gathered for the query: \"${plan.originalQuery}\"`;\n            }\n            // Create a basic summary\n            let summary = `Based on browsing research for \"${plan.originalQuery}\":\\n\\n`;\n            completedTasks.forEach((task, index)=>{\n                summary += `${index + 1}. ${task.description}:\\n`;\n                if (task.result?.results && Array.isArray(task.result.results)) {\n                    task.result.results.slice(0, 3).forEach((result)=>{\n                        summary += `   • ${result.title || 'Result'}\\n`;\n                    });\n                } else if (typeof task.result === 'string') {\n                    summary += `   ${task.result.substring(0, 200)}...\\n`;\n                }\n                summary += '\\n';\n            });\n            return summary;\n        } catch (error) {\n            console.error('[Smart Browsing] Error synthesizing final result:', error);\n            return `Research completed for \"${plan.originalQuery}\" but encountered errors in synthesis. Please check the individual results.`;\n        }\n    }\n    /**\n   * Log the browsing plan for debugging\n   */ logPlan(plan) {\n        console.log(`[Smart Browsing] 📋 BROWSING PLAN:`);\n        console.log(`[Smart Browsing] Goal: ${plan.goal}`);\n        console.log(`[Smart Browsing] Subtasks:`);\n        plan.subtasks.forEach((subtask, index)=>{\n            console.log(`[Smart Browsing]   ${index + 1}. [${subtask.type.toUpperCase()}] ${subtask.description}`);\n            console.log(`[Smart Browsing]      Query: \"${subtask.query}\"`);\n            console.log(`[Smart Browsing]      Priority: ${subtask.priority}, Status: ${subtask.status}`);\n            if (subtask.searchTerms && subtask.searchTerms.length > 0) {\n                console.log(`[Smart Browsing]      Alt terms: ${subtask.searchTerms.join(', ')}`);\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\n");

/***/ })

};
;