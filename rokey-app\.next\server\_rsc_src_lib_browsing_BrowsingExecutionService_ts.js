"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        const keysString = process.env.BROWSERLESS_API_KEYS;\n        if (!keysString) {\n            console.error('BROWSERLESS_API_KEYS not found in environment variables');\n            return;\n        }\n        this.apiKeys = keysString.split(',').map((key)=>key.trim()).filter(Boolean);\n        console.log(`Initialized Browserless service with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            if (elements.length > 0) {\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          return Array.from(elements).slice(0, 5).map(el => ({\n            title: el.textContent?.trim() || '',\n            link: el.closest('a')?.href || el.href || ''\n          })).filter(item => item.title && item.link);\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            // Sort models by order for fallback\n            const sortedModels = [\n                ...browsingConfig.browsing_models\n            ].sort((a, b)=>a.order - b.order);\n            console.log(`[Browsing Execution] Starting browsing with ${sortedModels.length} models configured`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            let lastError = null;\n            // Try each model in order (strict fallback pattern)\n            for (const model of sortedModels){\n                try {\n                    console.log(`[Browsing Execution] Attempting with ${model.provider}/${model.model}`);\n                    // First, perform the web browsing\n                    const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                    if (!browsingResult.success) {\n                        throw new Error(browsingResult.error || 'Browsing failed');\n                    }\n                    console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);\n                    console.log(`[Browsing Execution] Browsing data preview:`, JSON.stringify(browsingResult.data, null, 2).substring(0, 500) + '...');\n                    // Then, use the AI model to process the browsing results\n                    const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                    if (aiResult.success) {\n                        console.log(`[Browsing Execution] ✅ Success with ${model.provider}/${model.model}`);\n                        return {\n                            success: true,\n                            content: aiResult.content,\n                            modelUsed: model.model,\n                            providerUsed: model.provider,\n                            browsingData: browsingResult.data\n                        };\n                    } else {\n                        throw new Error(aiResult.error || 'AI processing failed');\n                    }\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                    lastError = errorMessage;\n                    console.warn(`[Browsing Execution] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                    continue;\n                }\n            }\n            // All models failed\n            return {\n                success: false,\n                error: `All browsing models failed. Last error: ${lastError}`\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtract(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtract(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        return `You are an AI assistant that processes web browsing results to answer user queries.\n\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\nWEB BROWSING RESULTS:\n${dataStr}\n\nINSTRUCTIONS:\n1. Analyze the browsing results carefully\n2. Extract relevant information that answers the user's query\n3. Provide a comprehensive, well-structured response\n4. If the browsing results don't contain relevant information, say so clearly\n5. Include specific details, numbers, dates, and facts from the browsing results\n6. Organize the information in a clear, readable format\n7. Cite sources when possible (URLs, website names, etc.)\n\nPlease provide a helpful response based on the browsing results:`;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: model.model,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: model.model,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: model.model,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            console.log(`[Browsing AI] Calling ${model.provider} API with model ${model.model}...`);\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)\n                content = result.choices?.[0]?.message?.content;\n            }\n            console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);\n            if (!content || content.trim().length === 0) {\n                console.error(`[Browsing AI] No content extracted from response. Full response:`, result);\n                return {\n                    error: 'No content returned from AI model - empty response'\n                };\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2Jyb3dzaW5nL0Jyb3dzaW5nRXhlY3V0aW9uU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxzRkFBc0Y7QUFDdEYsNkRBQTZEO0FBRVY7QUFpQjVDLE1BQU1DO0lBSVhDLGFBQWM7UUFDWixJQUFJLENBQUNDLGtCQUFrQixHQUFHSCx3REFBa0JBLENBQUNJLFdBQVc7SUFDMUQ7SUFFQSxPQUFPQSxjQUF3QztRQUM3QyxJQUFJLENBQUNILHlCQUF5QkksUUFBUSxFQUFFO1lBQ3RDSix5QkFBeUJJLFFBQVEsR0FBRyxJQUFJSjtRQUMxQztRQUNBLE9BQU9BLHlCQUF5QkksUUFBUTtJQUMxQztJQUVBOztHQUVDLEdBQ0QsTUFBTUMsZ0JBQ0pDLEtBQWEsRUFDYkMsY0FBOEIsRUFDOUJDLGVBQWtELFFBQVEsRUFDMURDLFlBQXFCLEVBQ2E7UUFDbEMsSUFBSTtZQUNGLElBQUksQ0FBQ0YsZUFBZUcsZ0JBQWdCLEVBQUU7Z0JBQ3BDLE9BQU87b0JBQ0xDLFNBQVM7b0JBQ1RDLE9BQU87Z0JBQ1Q7WUFDRjtZQUVBLElBQUksQ0FBQ0wsZUFBZU0sZUFBZSxJQUFJTixlQUFlTSxlQUFlLENBQUNDLE1BQU0sS0FBSyxHQUFHO2dCQUNsRixPQUFPO29CQUNMSCxTQUFTO29CQUNUQyxPQUFPO2dCQUNUO1lBQ0Y7WUFFQSxvQ0FBb0M7WUFDcEMsTUFBTUcsZUFBZTttQkFBSVIsZUFBZU0sZUFBZTthQUFDLENBQUNHLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFRSxLQUFLLEdBQUdELEVBQUVDLEtBQUs7WUFFekZDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDRDQUE0QyxFQUFFTixhQUFhRCxNQUFNLENBQUMsa0JBQWtCLENBQUM7WUFDbEdNLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDZCQUE2QixFQUFFZixNQUFNLFNBQVMsRUFBRUUsY0FBYztZQUUzRSxJQUFJYyxZQUEyQjtZQUUvQixvREFBb0Q7WUFDcEQsS0FBSyxNQUFNQyxTQUFTUixhQUFjO2dCQUNoQyxJQUFJO29CQUNGSyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxxQ0FBcUMsRUFBRUUsTUFBTUMsUUFBUSxDQUFDLENBQUMsRUFBRUQsTUFBTUEsS0FBSyxFQUFFO29CQUVuRixrQ0FBa0M7b0JBQ2xDLE1BQU1FLGlCQUFpQixNQUFNLElBQUksQ0FBQ0Msa0JBQWtCLENBQUNqQixnQkFBZ0JILE9BQU9FO29CQUU1RSxJQUFJLENBQUNpQixlQUFlZCxPQUFPLEVBQUU7d0JBQzNCLE1BQU0sSUFBSWdCLE1BQU1GLGVBQWViLEtBQUssSUFBSTtvQkFDMUM7b0JBRUFRLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9EQUFvRCxFQUFFTyxLQUFLQyxTQUFTLENBQUNKLGVBQWVLLElBQUksRUFBRWhCLE1BQU0sQ0FBQyxtQkFBbUIsQ0FBQztvQkFDbElNLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJDQUEyQyxDQUFDLEVBQUVPLEtBQUtDLFNBQVMsQ0FBQ0osZUFBZUssSUFBSSxFQUFFLE1BQU0sR0FBR0MsU0FBUyxDQUFDLEdBQUcsT0FBTztvQkFFNUgseURBQXlEO29CQUN6RCxNQUFNQyxXQUFXLE1BQU0sSUFBSSxDQUFDQyxhQUFhLENBQ3ZDM0IsT0FDQW1CLGVBQWVLLElBQUksRUFDbkJQLE9BQ0FmO29CQUdGLElBQUl3QixTQUFTckIsT0FBTyxFQUFFO3dCQUNwQlMsUUFBUUMsR0FBRyxDQUFDLENBQUMsb0NBQW9DLEVBQUVFLE1BQU1DLFFBQVEsQ0FBQyxDQUFDLEVBQUVELE1BQU1BLEtBQUssRUFBRTt3QkFDbEYsT0FBTzs0QkFDTFosU0FBUzs0QkFDVHVCLFNBQVNGLFNBQVNFLE9BQU87NEJBQ3pCQyxXQUFXWixNQUFNQSxLQUFLOzRCQUN0QmEsY0FBY2IsTUFBTUMsUUFBUTs0QkFDNUJhLGNBQWNaLGVBQWVLLElBQUk7d0JBQ25DO29CQUNGLE9BQU87d0JBQ0wsTUFBTSxJQUFJSCxNQUFNSyxTQUFTcEIsS0FBSyxJQUFJO29CQUNwQztnQkFFRixFQUFFLE9BQU9BLE9BQU87b0JBQ2QsTUFBTTBCLGVBQWUxQixpQkFBaUJlLFFBQVFmLE1BQU0yQixPQUFPLEdBQUc7b0JBQzlEakIsWUFBWWdCO29CQUVabEIsUUFBUW9CLElBQUksQ0FBQyxDQUFDLG1DQUFtQyxFQUFFakIsTUFBTUMsUUFBUSxDQUFDLENBQUMsRUFBRUQsTUFBTUEsS0FBSyxDQUFDLEVBQUUsRUFBRWUsY0FBYztvQkFHbkc7Z0JBQ0Y7WUFDRjtZQUVBLG9CQUFvQjtZQUNwQixPQUFPO2dCQUNMM0IsU0FBUztnQkFDVEMsT0FBTyxDQUFDLHdDQUF3QyxFQUFFVSxXQUFXO1lBQy9EO1FBRUYsRUFBRSxPQUFPVixPQUFPO1lBQ2QsTUFBTTBCLGVBQWUxQixpQkFBaUJlLFFBQVFmLE1BQU0yQixPQUFPLEdBQUc7WUFDOURuQixRQUFRUixLQUFLLENBQUMscUNBQXFDMEI7WUFDbkQsT0FBTztnQkFDTDNCLFNBQVM7Z0JBQ1RDLE9BQU8sQ0FBQywyQkFBMkIsRUFBRTBCLGNBQWM7WUFDckQ7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjWixtQkFDWnBCLEtBQWEsRUFDYkUsWUFBK0MsRUFDWTtRQUMzRCxJQUFJO1lBQ0YsSUFBSWlDO1lBRUosT0FBUWpDO2dCQUNOLEtBQUs7b0JBQ0gsMkJBQTJCO29CQUMzQmlDLFNBQVMsTUFBTSxJQUFJLENBQUN2QyxrQkFBa0IsQ0FBQ3dDLGdCQUFnQixDQUFDcEM7b0JBQ3hEO2dCQUVGLEtBQUs7b0JBQ0gsNkNBQTZDO29CQUM3QyxNQUFNcUMsV0FBV3JDLE1BQU1zQyxLQUFLLENBQUM7b0JBQzdCLE1BQU1DLE1BQU1GLFdBQVdBLFFBQVEsQ0FBQyxFQUFFLEdBQUdyQztvQkFDckNtQyxTQUFTLE1BQU0sSUFBSSxDQUFDdkMsa0JBQWtCLENBQUM0QyxrQkFBa0IsQ0FBQ0Q7b0JBQzFEO2dCQUVGLEtBQUs7b0JBQ0gsbURBQW1EO29CQUNuRCxNQUFNRSxhQUFhekMsTUFBTXNDLEtBQUssQ0FBQyxzQkFBc0IsQ0FBQyxFQUFFLElBQUl0QztvQkFDNURtQyxTQUFTLE1BQU0sSUFBSSxDQUFDdkMsa0JBQWtCLENBQUM0QyxrQkFBa0IsQ0FBQ0M7b0JBQzFEO2dCQUVGO29CQUNFLG9CQUFvQjtvQkFDcEJOLFNBQVMsTUFBTSxJQUFJLENBQUN2QyxrQkFBa0IsQ0FBQ3dDLGdCQUFnQixDQUFDcEM7WUFDNUQ7WUFFQSxJQUFJbUMsVUFBVUEsT0FBT1gsSUFBSSxFQUFFO2dCQUN6QlYsUUFBUUMsR0FBRyxDQUFDLENBQUMsb0RBQW9ELEVBQUVPLEtBQUtDLFNBQVMsQ0FBQ1ksT0FBT1gsSUFBSSxFQUFFaEIsTUFBTSxDQUFDLG1CQUFtQixDQUFDO2dCQUMxSCxPQUFPO29CQUFFSCxTQUFTO29CQUFNbUIsTUFBTVcsT0FBT1gsSUFBSTtnQkFBQztZQUM1QyxPQUFPO2dCQUNMLE9BQU87b0JBQUVuQixTQUFTO29CQUFPQyxPQUFPO2dCQUFpQztZQUNuRTtRQUVGLEVBQUUsT0FBT0EsT0FBTztZQUNkLE1BQU0wQixlQUFlMUIsaUJBQWlCZSxRQUFRZixNQUFNMkIsT0FBTyxHQUFHO1lBQzlEbkIsUUFBUVIsS0FBSyxDQUFDLDZDQUE2QzBCO1lBQzNELE9BQU87Z0JBQUUzQixTQUFTO2dCQUFPQyxPQUFPMEI7WUFBYTtRQUMvQztJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjTCxjQUNaZSxhQUFxQixFQUNyQlgsWUFBaUIsRUFDakJkLEtBQW9CLEVBQ3BCZixZQUFvQixFQUM2QztRQUNqRSxJQUFJO1lBQ0YsTUFBTXlDLFNBQVMsSUFBSSxDQUFDQyxxQkFBcUIsQ0FBQ0YsZUFBZVgsY0FBYzdCO1lBRXZFLG1DQUFtQztZQUNuQyxNQUFNMkMsV0FBVyxNQUFNLElBQUksQ0FBQ0MsY0FBYyxDQUFDSCxRQUFRMUI7WUFFbkQsSUFBSTRCLFlBQVlBLFNBQVNqQixPQUFPLEVBQUU7Z0JBQ2hDLE9BQU87b0JBQUV2QixTQUFTO29CQUFNdUIsU0FBU2lCLFNBQVNqQixPQUFPO2dCQUFDO1lBQ3BELE9BQU87Z0JBQ0wsT0FBTztvQkFBRXZCLFNBQVM7b0JBQU9DLE9BQU87Z0JBQW9DO1lBQ3RFO1FBRUYsRUFBRSxPQUFPQSxPQUFPO1lBQ2QsTUFBTTBCLGVBQWUxQixpQkFBaUJlLFFBQVFmLE1BQU0yQixPQUFPLEdBQUc7WUFDOUQsT0FBTztnQkFBRTVCLFNBQVM7Z0JBQU9DLE9BQU8wQjtZQUFhO1FBQy9DO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHNCQUE4QlUsYUFBcUIsRUFBRVgsWUFBaUIsRUFBRTdCLFlBQW9CLEVBQVU7UUFDcEcsTUFBTTZDLFVBQVV6QixLQUFLQyxTQUFTLENBQUNRLGNBQWMsTUFBTTtRQUVuRCxPQUFPLENBQUM7O2FBRUMsRUFBRVcsY0FBYztlQUNkLEVBQUV4QyxhQUFhOzs7QUFHOUIsRUFBRTZDLFFBQVE7Ozs7Ozs7Ozs7O2dFQVdzRCxDQUFDO0lBQy9EO0lBRUE7O0dBRUMsR0FDRCxNQUFjRCxlQUNaSCxNQUFjLEVBQ2QxQixLQUFvQixFQUMyQjtRQUMvQyxJQUFJO1lBQ0YsTUFBTStCLFdBQVc7Z0JBQ2Y7b0JBQ0VDLE1BQU07b0JBQ05yQixTQUFTZTtnQkFDWDthQUNEO1lBRUQsSUFBSU87WUFDSixJQUFJQztZQUNKLElBQUlDO1lBRUosdUNBQXVDO1lBQ3ZDLE9BQVFuQyxNQUFNQyxRQUFRO2dCQUNwQixLQUFLO29CQUNIZ0MsU0FBUztvQkFDVEMsVUFBVTt3QkFDUixnQkFBZ0I7d0JBQ2hCLGlCQUFpQixDQUFDLE9BQU8sRUFBRWxDLE1BQU1vQyxPQUFPLEVBQUU7b0JBQzVDO29CQUNBRCxPQUFPO3dCQUNMbkMsT0FBT0EsTUFBTUEsS0FBSzt3QkFDbEIrQjt3QkFDQU0sYUFBYXJDLE1BQU1xQyxXQUFXLElBQUk7d0JBQ2xDQyxZQUFZO29CQUNkO29CQUNBO2dCQUVGLEtBQUs7b0JBQ0hMLFNBQVM7b0JBQ1RDLFVBQVU7d0JBQ1IsZ0JBQWdCO3dCQUNoQixhQUFhbEMsTUFBTW9DLE9BQU87d0JBQzFCLHFCQUFxQjtvQkFDdkI7b0JBQ0FELE9BQU87d0JBQ0xuQyxPQUFPQSxNQUFNQSxLQUFLO3dCQUNsQitCO3dCQUNBTSxhQUFhckMsTUFBTXFDLFdBQVcsSUFBSTt3QkFDbENDLFlBQVk7b0JBQ2Q7b0JBQ0E7Z0JBRUYsS0FBSztvQkFDSEwsU0FBUztvQkFDVEMsVUFBVTt3QkFDUixnQkFBZ0I7d0JBQ2hCLGlCQUFpQixDQUFDLE9BQU8sRUFBRWxDLE1BQU1vQyxPQUFPLEVBQUU7b0JBQzVDO29CQUNBRCxPQUFPO3dCQUNMbkMsT0FBT0EsTUFBTUEsS0FBSzt3QkFDbEIrQjt3QkFDQU0sYUFBYXJDLE1BQU1xQyxXQUFXLElBQUk7d0JBQ2xDQyxZQUFZO29CQUNkO29CQUNBO2dCQUVGO29CQUNFLE1BQU0sSUFBSWxDLE1BQU0sQ0FBQyxzQkFBc0IsRUFBRUosTUFBTUMsUUFBUSxFQUFFO1lBQzdEO1lBRUFKLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFRSxNQUFNQyxRQUFRLENBQUMsZ0JBQWdCLEVBQUVELE1BQU1BLEtBQUssQ0FBQyxHQUFHLENBQUM7WUFFdEYsTUFBTTRCLFdBQVcsTUFBTVcsTUFBTU4sUUFBUTtnQkFDbkNPLFFBQVE7Z0JBQ1JOO2dCQUNBQyxNQUFNOUIsS0FBS0MsU0FBUyxDQUFDNkI7Z0JBQ3JCTSxRQUFRQyxZQUFZQyxPQUFPLENBQUMsT0FBTyxjQUFjO1lBQ25EO1lBRUEsSUFBSSxDQUFDZixTQUFTZ0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1qQixTQUFTa0IsSUFBSTtnQkFDckNqRCxRQUFRUixLQUFLLENBQUMsQ0FBQyx5QkFBeUIsRUFBRXVDLFNBQVNtQixNQUFNLENBQUMsR0FBRyxFQUFFRixXQUFXO2dCQUMxRSxNQUFNLElBQUl6QyxNQUFNLENBQUMsV0FBVyxFQUFFd0IsU0FBU21CLE1BQU0sQ0FBQyxHQUFHLEVBQUVGLFdBQVc7WUFDaEU7WUFFQSxNQUFNM0IsU0FBUyxNQUFNVSxTQUFTb0IsSUFBSTtZQUNsQ25ELFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJCQUEyQixDQUFDLEVBQUVPLEtBQUtDLFNBQVMsQ0FBQ1ksUUFBUSxNQUFNO1lBRXhFLG9EQUFvRDtZQUNwRCxJQUFJUDtZQUVKLElBQUlYLE1BQU1DLFFBQVEsS0FBSyxhQUFhO2dCQUNsQ1UsVUFBVU8sT0FBT1AsT0FBTyxFQUFFLENBQUMsRUFBRSxFQUFFbUM7WUFDakMsT0FBTztnQkFDTCw2RUFBNkU7Z0JBQzdFbkMsVUFBVU8sT0FBTytCLE9BQU8sRUFBRSxDQUFDLEVBQUUsRUFBRWpDLFNBQVNMO1lBQzFDO1lBRUFkLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHdDQUF3QyxFQUFFYSxTQUFTcEIsVUFBVSxHQUFHO1lBRTdFLElBQUksQ0FBQ29CLFdBQVdBLFFBQVF1QyxJQUFJLEdBQUczRCxNQUFNLEtBQUssR0FBRztnQkFDM0NNLFFBQVFSLEtBQUssQ0FBQyxDQUFDLGdFQUFnRSxDQUFDLEVBQUU2QjtnQkFDbEYsT0FBTztvQkFBRTdCLE9BQU87Z0JBQXFEO1lBQ3ZFO1lBRUEsT0FBTztnQkFBRXNCO1lBQVE7UUFFbkIsRUFBRSxPQUFPdEIsT0FBTztZQUNkLE1BQU0wQixlQUFlMUIsaUJBQWlCZSxRQUFRZixNQUFNMkIsT0FBTyxHQUFHO1lBQzlEbkIsUUFBUVIsS0FBSyxDQUFDLENBQUMsd0NBQXdDLENBQUMsRUFBRTBCO1lBQzFELE9BQU87Z0JBQUUxQixPQUFPMEI7WUFBYTtRQUMvQjtJQUNGO0FBQ0Y7QUFFQSxpRUFBZXRDLHdCQUF3QkEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGxpYlxcYnJvd3NpbmdcXEJyb3dzaW5nRXhlY3V0aW9uU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBCcm93c2luZyBFeGVjdXRpb24gU2VydmljZSAtIEhhbmRsZXMgYnJvd3NpbmcgbW9kZWwgc2VsZWN0aW9uIHdpdGggZmFsbGJhY2sgc3VwcG9ydFxuLy8gSW50ZWdyYXRlcyB3aXRoIEJyb3dzZXJsZXNzU2VydmljZSBmb3IgYWN0dWFsIHdlYiBicm93c2luZ1xuXG5pbXBvcnQgQnJvd3Nlcmxlc3NTZXJ2aWNlIGZyb20gJ0AvbGliL2Jyb3dzZXJsZXNzJztcbmltcG9ydCB7IEJyb3dzaW5nTW9kZWwgfSBmcm9tICdAL3R5cGVzL2N1c3RvbUFwaUNvbmZpZ3MnO1xuXG5pbnRlcmZhY2UgQnJvd3NpbmdFeGVjdXRpb25SZXN1bHQge1xuICBzdWNjZXNzOiBib29sZWFuO1xuICBjb250ZW50Pzogc3RyaW5nO1xuICBlcnJvcj86IHN0cmluZztcbiAgbW9kZWxVc2VkPzogc3RyaW5nO1xuICBwcm92aWRlclVzZWQ/OiBzdHJpbmc7XG4gIGJyb3dzaW5nRGF0YT86IGFueTtcbn1cblxuaW50ZXJmYWNlIEJyb3dzaW5nQ29uZmlnIHtcbiAgYnJvd3NpbmdfZW5hYmxlZDogYm9vbGVhbjtcbiAgYnJvd3NpbmdfbW9kZWxzOiBCcm93c2luZ01vZGVsW107XG59XG5cbmV4cG9ydCBjbGFzcyBCcm93c2luZ0V4ZWN1dGlvblNlcnZpY2Uge1xuICBwcml2YXRlIHN0YXRpYyBpbnN0YW5jZTogQnJvd3NpbmdFeGVjdXRpb25TZXJ2aWNlO1xuICBwcml2YXRlIGJyb3dzZXJsZXNzU2VydmljZTogQnJvd3Nlcmxlc3NTZXJ2aWNlO1xuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuYnJvd3Nlcmxlc3NTZXJ2aWNlID0gQnJvd3Nlcmxlc3NTZXJ2aWNlLmdldEluc3RhbmNlKCk7XG4gIH1cblxuICBzdGF0aWMgZ2V0SW5zdGFuY2UoKTogQnJvd3NpbmdFeGVjdXRpb25TZXJ2aWNlIHtcbiAgICBpZiAoIUJyb3dzaW5nRXhlY3V0aW9uU2VydmljZS5pbnN0YW5jZSkge1xuICAgICAgQnJvd3NpbmdFeGVjdXRpb25TZXJ2aWNlLmluc3RhbmNlID0gbmV3IEJyb3dzaW5nRXhlY3V0aW9uU2VydmljZSgpO1xuICAgIH1cbiAgICByZXR1cm4gQnJvd3NpbmdFeGVjdXRpb25TZXJ2aWNlLmluc3RhbmNlO1xuICB9XG5cbiAgLyoqXG4gICAqIEV4ZWN1dGUgYnJvd3Npbmcgd2l0aCBtb2RlbCBmYWxsYmFjayBzdXBwb3J0XG4gICAqL1xuICBhc3luYyBleGVjdXRlQnJvd3NpbmcoXG4gICAgcXVlcnk6IHN0cmluZyxcbiAgICBicm93c2luZ0NvbmZpZzogQnJvd3NpbmdDb25maWcsXG4gICAgYnJvd3NpbmdUeXBlOiAnc2VhcmNoJyB8ICduYXZpZ2F0ZScgfCAnZXh0cmFjdCcgPSAnc2VhcmNoJyxcbiAgICByZWZpbmVkUXVlcnk/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTxCcm93c2luZ0V4ZWN1dGlvblJlc3VsdD4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIWJyb3dzaW5nQ29uZmlnLmJyb3dzaW5nX2VuYWJsZWQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogJ0Jyb3dzaW5nIGlzIG5vdCBlbmFibGVkIGZvciB0aGlzIGNvbmZpZ3VyYXRpb24nXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIGlmICghYnJvd3NpbmdDb25maWcuYnJvd3NpbmdfbW9kZWxzIHx8IGJyb3dzaW5nQ29uZmlnLmJyb3dzaW5nX21vZGVscy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogJ05vIGJyb3dzaW5nIG1vZGVscyBjb25maWd1cmVkJ1xuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgICAvLyBTb3J0IG1vZGVscyBieSBvcmRlciBmb3IgZmFsbGJhY2tcbiAgICAgIGNvbnN0IHNvcnRlZE1vZGVscyA9IFsuLi5icm93c2luZ0NvbmZpZy5icm93c2luZ19tb2RlbHNdLnNvcnQoKGEsIGIpID0+IGEub3JkZXIgLSBiLm9yZGVyKTtcbiAgICAgIFxuICAgICAgY29uc29sZS5sb2coYFtCcm93c2luZyBFeGVjdXRpb25dIFN0YXJ0aW5nIGJyb3dzaW5nIHdpdGggJHtzb3J0ZWRNb2RlbHMubGVuZ3RofSBtb2RlbHMgY29uZmlndXJlZGApO1xuICAgICAgY29uc29sZS5sb2coYFtCcm93c2luZyBFeGVjdXRpb25dIFF1ZXJ5OiBcIiR7cXVlcnl9XCIsIFR5cGU6ICR7YnJvd3NpbmdUeXBlfWApO1xuXG4gICAgICBsZXQgbGFzdEVycm9yOiBzdHJpbmcgfCBudWxsID0gbnVsbDtcblxuICAgICAgLy8gVHJ5IGVhY2ggbW9kZWwgaW4gb3JkZXIgKHN0cmljdCBmYWxsYmFjayBwYXR0ZXJuKVxuICAgICAgZm9yIChjb25zdCBtb2RlbCBvZiBzb3J0ZWRNb2RlbHMpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW0Jyb3dzaW5nIEV4ZWN1dGlvbl0gQXR0ZW1wdGluZyB3aXRoICR7bW9kZWwucHJvdmlkZXJ9LyR7bW9kZWwubW9kZWx9YCk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gRmlyc3QsIHBlcmZvcm0gdGhlIHdlYiBicm93c2luZ1xuICAgICAgICAgIGNvbnN0IGJyb3dzaW5nUmVzdWx0ID0gYXdhaXQgdGhpcy5wZXJmb3JtV2ViQnJvd3NpbmcocmVmaW5lZFF1ZXJ5IHx8IHF1ZXJ5LCBicm93c2luZ1R5cGUpO1xuXG4gICAgICAgICAgaWYgKCFicm93c2luZ1Jlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYnJvd3NpbmdSZXN1bHQuZXJyb3IgfHwgJ0Jyb3dzaW5nIGZhaWxlZCcpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnNvbGUubG9nKGBbQnJvd3NpbmcgRXhlY3V0aW9uXSDinIUgV2ViIGJyb3dzaW5nIHN1Y2Nlc3NmdWwsIGdvdCAke0pTT04uc3RyaW5naWZ5KGJyb3dzaW5nUmVzdWx0LmRhdGEpLmxlbmd0aH0gY2hhcmFjdGVycyBvZiBkYXRhYCk7XG4gICAgICAgICAgY29uc29sZS5sb2coYFtCcm93c2luZyBFeGVjdXRpb25dIEJyb3dzaW5nIGRhdGEgcHJldmlldzpgLCBKU09OLnN0cmluZ2lmeShicm93c2luZ1Jlc3VsdC5kYXRhLCBudWxsLCAyKS5zdWJzdHJpbmcoMCwgNTAwKSArICcuLi4nKTtcblxuICAgICAgICAgIC8vIFRoZW4sIHVzZSB0aGUgQUkgbW9kZWwgdG8gcHJvY2VzcyB0aGUgYnJvd3NpbmcgcmVzdWx0c1xuICAgICAgICAgIGNvbnN0IGFpUmVzdWx0ID0gYXdhaXQgdGhpcy5wcm9jZXNzV2l0aEFJKFxuICAgICAgICAgICAgcXVlcnksXG4gICAgICAgICAgICBicm93c2luZ1Jlc3VsdC5kYXRhLFxuICAgICAgICAgICAgbW9kZWwsXG4gICAgICAgICAgICBicm93c2luZ1R5cGVcbiAgICAgICAgICApO1xuXG4gICAgICAgICAgaWYgKGFpUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbQnJvd3NpbmcgRXhlY3V0aW9uXSDinIUgU3VjY2VzcyB3aXRoICR7bW9kZWwucHJvdmlkZXJ9LyR7bW9kZWwubW9kZWx9YCk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICBjb250ZW50OiBhaVJlc3VsdC5jb250ZW50LFxuICAgICAgICAgICAgICBtb2RlbFVzZWQ6IG1vZGVsLm1vZGVsLFxuICAgICAgICAgICAgICBwcm92aWRlclVzZWQ6IG1vZGVsLnByb3ZpZGVyLFxuICAgICAgICAgICAgICBicm93c2luZ0RhdGE6IGJyb3dzaW5nUmVzdWx0LmRhdGFcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihhaVJlc3VsdC5lcnJvciB8fCAnQUkgcHJvY2Vzc2luZyBmYWlsZWQnKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJztcbiAgICAgICAgICBsYXN0RXJyb3IgPSBlcnJvck1lc3NhZ2U7XG4gICAgICAgICAgXG4gICAgICAgICAgY29uc29sZS53YXJuKGBbQnJvd3NpbmcgRXhlY3V0aW9uXSDinYwgRmFpbGVkIHdpdGggJHttb2RlbC5wcm92aWRlcn0vJHttb2RlbC5tb2RlbH06ICR7ZXJyb3JNZXNzYWdlfWApO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIENvbnRpbnVlIHRvIG5leHQgbW9kZWwgaW4gZmFsbGJhY2sgY2hhaW5cbiAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBBbGwgbW9kZWxzIGZhaWxlZFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiBgQWxsIGJyb3dzaW5nIG1vZGVscyBmYWlsZWQuIExhc3QgZXJyb3I6ICR7bGFzdEVycm9yfWBcbiAgICAgIH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcic7XG4gICAgICBjb25zb2xlLmVycm9yKCdbQnJvd3NpbmcgRXhlY3V0aW9uXSBGYXRhbCBlcnJvcjonLCBlcnJvck1lc3NhZ2UpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiBgQnJvd3NpbmcgZXhlY3V0aW9uIGZhaWxlZDogJHtlcnJvck1lc3NhZ2V9YFxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUGVyZm9ybSB3ZWIgYnJvd3NpbmcgdXNpbmcgQnJvd3Nlcmxlc3NTZXJ2aWNlXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIHBlcmZvcm1XZWJCcm93c2luZyhcbiAgICBxdWVyeTogc3RyaW5nLFxuICAgIGJyb3dzaW5nVHlwZTogJ3NlYXJjaCcgfCAnbmF2aWdhdGUnIHwgJ2V4dHJhY3QnXG4gICk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBkYXRhPzogYW55OyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGxldCByZXN1bHQ7XG5cbiAgICAgIHN3aXRjaCAoYnJvd3NpbmdUeXBlKSB7XG4gICAgICAgIGNhc2UgJ3NlYXJjaCc6XG4gICAgICAgICAgLy8gVXNlIHNlYXJjaCBmdW5jdGlvbmFsaXR5XG4gICAgICAgICAgcmVzdWx0ID0gYXdhaXQgdGhpcy5icm93c2VybGVzc1NlcnZpY2Uuc2VhcmNoQW5kRXh0cmFjdChxdWVyeSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgXG4gICAgICAgIGNhc2UgJ25hdmlnYXRlJzpcbiAgICAgICAgICAvLyBUcnkgdG8gZXh0cmFjdCBVUkwgZnJvbSBxdWVyeSBvciB1c2UgYXMtaXNcbiAgICAgICAgICBjb25zdCB1cmxNYXRjaCA9IHF1ZXJ5Lm1hdGNoKC9odHRwcz86XFwvXFwvW15cXHNdKy8pO1xuICAgICAgICAgIGNvbnN0IHVybCA9IHVybE1hdGNoID8gdXJsTWF0Y2hbMF0gOiBxdWVyeTtcbiAgICAgICAgICByZXN1bHQgPSBhd2FpdCB0aGlzLmJyb3dzZXJsZXNzU2VydmljZS5uYXZpZ2F0ZUFuZEV4dHJhY3QodXJsKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgICBcbiAgICAgICAgY2FzZSAnZXh0cmFjdCc6XG4gICAgICAgICAgLy8gU2ltaWxhciB0byBuYXZpZ2F0ZSBidXQgd2l0aCBzcGVjaWZpYyBleHRyYWN0aW9uXG4gICAgICAgICAgY29uc3QgZXh0cmFjdFVybCA9IHF1ZXJ5Lm1hdGNoKC9odHRwcz86XFwvXFwvW15cXHNdKy8pPy5bMF0gfHwgcXVlcnk7XG4gICAgICAgICAgcmVzdWx0ID0gYXdhaXQgdGhpcy5icm93c2VybGVzc1NlcnZpY2UubmF2aWdhdGVBbmRFeHRyYWN0KGV4dHJhY3RVcmwpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIFxuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIC8vIERlZmF1bHQgdG8gc2VhcmNoXG4gICAgICAgICAgcmVzdWx0ID0gYXdhaXQgdGhpcy5icm93c2VybGVzc1NlcnZpY2Uuc2VhcmNoQW5kRXh0cmFjdChxdWVyeSk7XG4gICAgICB9XG5cbiAgICAgIGlmIChyZXN1bHQgJiYgcmVzdWx0LmRhdGEpIHtcbiAgICAgICAgY29uc29sZS5sb2coYFtCcm93c2luZyBFeGVjdXRpb25dIOKchSBXZWIgYnJvd3Npbmcgc3VjY2Vzc2Z1bCwgZ290ICR7SlNPTi5zdHJpbmdpZnkocmVzdWx0LmRhdGEpLmxlbmd0aH0gY2hhcmFjdGVycyBvZiBkYXRhYCk7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3VsdC5kYXRhIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdObyBkYXRhIHJldHVybmVkIGZyb20gYnJvd3NpbmcnIH07XG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcic7XG4gICAgICBjb25zb2xlLmVycm9yKCdbQnJvd3NpbmcgRXhlY3V0aW9uXSBXZWIgYnJvd3NpbmcgZmFpbGVkOicsIGVycm9yTWVzc2FnZSk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBQcm9jZXNzIGJyb3dzaW5nIHJlc3VsdHMgd2l0aCBBSSBtb2RlbFxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBwcm9jZXNzV2l0aEFJKFxuICAgIG9yaWdpbmFsUXVlcnk6IHN0cmluZyxcbiAgICBicm93c2luZ0RhdGE6IGFueSxcbiAgICBtb2RlbDogQnJvd3NpbmdNb2RlbCxcbiAgICBicm93c2luZ1R5cGU6IHN0cmluZ1xuICApOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgY29udGVudD86IHN0cmluZzsgZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwcm9tcHQgPSB0aGlzLmJ1aWxkUHJvY2Vzc2luZ1Byb21wdChvcmlnaW5hbFF1ZXJ5LCBicm93c2luZ0RhdGEsIGJyb3dzaW5nVHlwZSk7XG4gICAgICBcbiAgICAgIC8vIENhbGwgdGhlIGFwcHJvcHJpYXRlIEFJIHByb3ZpZGVyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2FsbEFJUHJvdmlkZXIocHJvbXB0LCBtb2RlbCk7XG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5jb250ZW50KSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGNvbnRlbnQ6IHJlc3BvbnNlLmNvbnRlbnQgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ05vIGNvbnRlbnQgcmV0dXJuZWQgZnJvbSBBSSBtb2RlbCcgfTtcbiAgICAgIH1cblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJztcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEJ1aWxkIHByb2Nlc3NpbmcgcHJvbXB0IGZvciBBSSBtb2RlbFxuICAgKi9cbiAgcHJpdmF0ZSBidWlsZFByb2Nlc3NpbmdQcm9tcHQob3JpZ2luYWxRdWVyeTogc3RyaW5nLCBicm93c2luZ0RhdGE6IGFueSwgYnJvd3NpbmdUeXBlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IGRhdGFTdHIgPSBKU09OLnN0cmluZ2lmeShicm93c2luZ0RhdGEsIG51bGwsIDIpO1xuICAgIFxuICAgIHJldHVybiBgWW91IGFyZSBhbiBBSSBhc3Npc3RhbnQgdGhhdCBwcm9jZXNzZXMgd2ViIGJyb3dzaW5nIHJlc3VsdHMgdG8gYW5zd2VyIHVzZXIgcXVlcmllcy5cblxuVVNFUiBRVUVSWTogXCIke29yaWdpbmFsUXVlcnl9XCJcbkJST1dTSU5HIFRZUEU6ICR7YnJvd3NpbmdUeXBlfVxuXG5XRUIgQlJPV1NJTkcgUkVTVUxUUzpcbiR7ZGF0YVN0cn1cblxuSU5TVFJVQ1RJT05TOlxuMS4gQW5hbHl6ZSB0aGUgYnJvd3NpbmcgcmVzdWx0cyBjYXJlZnVsbHlcbjIuIEV4dHJhY3QgcmVsZXZhbnQgaW5mb3JtYXRpb24gdGhhdCBhbnN3ZXJzIHRoZSB1c2VyJ3MgcXVlcnlcbjMuIFByb3ZpZGUgYSBjb21wcmVoZW5zaXZlLCB3ZWxsLXN0cnVjdHVyZWQgcmVzcG9uc2VcbjQuIElmIHRoZSBicm93c2luZyByZXN1bHRzIGRvbid0IGNvbnRhaW4gcmVsZXZhbnQgaW5mb3JtYXRpb24sIHNheSBzbyBjbGVhcmx5XG41LiBJbmNsdWRlIHNwZWNpZmljIGRldGFpbHMsIG51bWJlcnMsIGRhdGVzLCBhbmQgZmFjdHMgZnJvbSB0aGUgYnJvd3NpbmcgcmVzdWx0c1xuNi4gT3JnYW5pemUgdGhlIGluZm9ybWF0aW9uIGluIGEgY2xlYXIsIHJlYWRhYmxlIGZvcm1hdFxuNy4gQ2l0ZSBzb3VyY2VzIHdoZW4gcG9zc2libGUgKFVSTHMsIHdlYnNpdGUgbmFtZXMsIGV0Yy4pXG5cblBsZWFzZSBwcm92aWRlIGEgaGVscGZ1bCByZXNwb25zZSBiYXNlZCBvbiB0aGUgYnJvd3NpbmcgcmVzdWx0czpgO1xuICB9XG5cbiAgLyoqXG4gICAqIENhbGwgQUkgcHJvdmlkZXIgYmFzZWQgb24gbW9kZWwgY29uZmlndXJhdGlvblxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBjYWxsQUlQcm92aWRlcihcbiAgICBwcm9tcHQ6IHN0cmluZyxcbiAgICBtb2RlbDogQnJvd3NpbmdNb2RlbFxuICApOiBQcm9taXNlPHsgY29udGVudD86IHN0cmluZzsgZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBtZXNzYWdlcyA9IFtcbiAgICAgICAge1xuICAgICAgICAgIHJvbGU6ICd1c2VyJyBhcyBjb25zdCxcbiAgICAgICAgICBjb250ZW50OiBwcm9tcHRcbiAgICAgICAgfVxuICAgICAgXTtcblxuICAgICAgbGV0IGFwaVVybDogc3RyaW5nO1xuICAgICAgbGV0IGhlYWRlcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XG4gICAgICBsZXQgYm9keTogYW55O1xuXG4gICAgICAvLyBDb25maWd1cmUgQVBJIGNhbGwgYmFzZWQgb24gcHJvdmlkZXJcbiAgICAgIHN3aXRjaCAobW9kZWwucHJvdmlkZXIpIHtcbiAgICAgICAgY2FzZSAnb3BlbmFpJzpcbiAgICAgICAgICBhcGlVcmwgPSAnaHR0cHM6Ly9hcGkub3BlbmFpLmNvbS92MS9jaGF0L2NvbXBsZXRpb25zJztcbiAgICAgICAgICBoZWFkZXJzID0ge1xuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke21vZGVsLmFwaV9rZXl9YFxuICAgICAgICAgIH07XG4gICAgICAgICAgYm9keSA9IHtcbiAgICAgICAgICAgIG1vZGVsOiBtb2RlbC5tb2RlbCxcbiAgICAgICAgICAgIG1lc3NhZ2VzLFxuICAgICAgICAgICAgdGVtcGVyYXR1cmU6IG1vZGVsLnRlbXBlcmF0dXJlIHx8IDAuMixcbiAgICAgICAgICAgIG1heF90b2tlbnM6IDIwMDBcbiAgICAgICAgICB9O1xuICAgICAgICAgIGJyZWFrO1xuXG4gICAgICAgIGNhc2UgJ2FudGhyb3BpYyc6XG4gICAgICAgICAgYXBpVXJsID0gJ2h0dHBzOi8vYXBpLmFudGhyb3BpYy5jb20vdjEvbWVzc2FnZXMnO1xuICAgICAgICAgIGhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgJ3gtYXBpLWtleSc6IG1vZGVsLmFwaV9rZXksXG4gICAgICAgICAgICAnYW50aHJvcGljLXZlcnNpb24nOiAnMjAyMy0wNi0wMSdcbiAgICAgICAgICB9O1xuICAgICAgICAgIGJvZHkgPSB7XG4gICAgICAgICAgICBtb2RlbDogbW9kZWwubW9kZWwsXG4gICAgICAgICAgICBtZXNzYWdlcyxcbiAgICAgICAgICAgIHRlbXBlcmF0dXJlOiBtb2RlbC50ZW1wZXJhdHVyZSB8fCAwLjIsXG4gICAgICAgICAgICBtYXhfdG9rZW5zOiAyMDAwXG4gICAgICAgICAgfTtcbiAgICAgICAgICBicmVhaztcblxuICAgICAgICBjYXNlICdnb29nbGUnOlxuICAgICAgICAgIGFwaVVybCA9ICdodHRwczovL2dlbmVyYXRpdmVsYW5ndWFnZS5nb29nbGVhcGlzLmNvbS92MWJldGEvb3BlbmFpL2NoYXQvY29tcGxldGlvbnMnO1xuICAgICAgICAgIGhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bW9kZWwuYXBpX2tleX1gXG4gICAgICAgICAgfTtcbiAgICAgICAgICBib2R5ID0ge1xuICAgICAgICAgICAgbW9kZWw6IG1vZGVsLm1vZGVsLFxuICAgICAgICAgICAgbWVzc2FnZXMsXG4gICAgICAgICAgICB0ZW1wZXJhdHVyZTogbW9kZWwudGVtcGVyYXR1cmUgfHwgMC4yLFxuICAgICAgICAgICAgbWF4X3Rva2VuczogMjAwMFxuICAgICAgICAgIH07XG4gICAgICAgICAgYnJlYWs7XG5cbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIHByb3ZpZGVyOiAke21vZGVsLnByb3ZpZGVyfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhgW0Jyb3dzaW5nIEFJXSBDYWxsaW5nICR7bW9kZWwucHJvdmlkZXJ9IEFQSSB3aXRoIG1vZGVsICR7bW9kZWwubW9kZWx9Li4uYCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYXBpVXJsLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShib2R5KSxcbiAgICAgICAgc2lnbmFsOiBBYm9ydFNpZ25hbC50aW1lb3V0KDMwMDAwKSAvLyAzMHMgdGltZW91dFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICBjb25zb2xlLmVycm9yKGBbQnJvd3NpbmcgQUldIEFQSSBlcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9IC0gJHtlcnJvclRleHR9YCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgQVBJIGVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c30gLSAke2Vycm9yVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coYFtCcm93c2luZyBBSV0gUmF3IHJlc3BvbnNlOmAsIEpTT04uc3RyaW5naWZ5KHJlc3VsdCwgbnVsbCwgMikpO1xuXG4gICAgICAvLyBFeHRyYWN0IGNvbnRlbnQgYmFzZWQgb24gcHJvdmlkZXIgcmVzcG9uc2UgZm9ybWF0XG4gICAgICBsZXQgY29udGVudDogc3RyaW5nIHwgdW5kZWZpbmVkO1xuXG4gICAgICBpZiAobW9kZWwucHJvdmlkZXIgPT09ICdhbnRocm9waWMnKSB7XG4gICAgICAgIGNvbnRlbnQgPSByZXN1bHQuY29udGVudD8uWzBdPy50ZXh0O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gRm9yIE9wZW5BSS1jb21wYXRpYmxlIEFQSXMgKGluY2x1ZGluZyBHb29nbGUncyBPcGVuQUktY29tcGF0aWJsZSBlbmRwb2ludClcbiAgICAgICAgY29udGVudCA9IHJlc3VsdC5jaG9pY2VzPy5bMF0/Lm1lc3NhZ2U/LmNvbnRlbnQ7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKGBbQnJvd3NpbmcgQUldIEV4dHJhY3RlZCBjb250ZW50IGxlbmd0aDogJHtjb250ZW50Py5sZW5ndGggfHwgMH1gKTtcblxuICAgICAgaWYgKCFjb250ZW50IHx8IGNvbnRlbnQudHJpbSgpLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBbQnJvd3NpbmcgQUldIE5vIGNvbnRlbnQgZXh0cmFjdGVkIGZyb20gcmVzcG9uc2UuIEZ1bGwgcmVzcG9uc2U6YCwgcmVzdWx0KTtcbiAgICAgICAgcmV0dXJuIHsgZXJyb3I6ICdObyBjb250ZW50IHJldHVybmVkIGZyb20gQUkgbW9kZWwgLSBlbXB0eSByZXNwb25zZScgfTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHsgY29udGVudCB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InO1xuICAgICAgY29uc29sZS5lcnJvcihgW0Jyb3dzaW5nIEFJXSBFcnJvciBjYWxsaW5nIEFJIHByb3ZpZGVyOmAsIGVycm9yTWVzc2FnZSk7XG4gICAgICByZXR1cm4geyBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IEJyb3dzaW5nRXhlY3V0aW9uU2VydmljZTtcbiJdLCJuYW1lcyI6WyJCcm93c2VybGVzc1NlcnZpY2UiLCJCcm93c2luZ0V4ZWN1dGlvblNlcnZpY2UiLCJjb25zdHJ1Y3RvciIsImJyb3dzZXJsZXNzU2VydmljZSIsImdldEluc3RhbmNlIiwiaW5zdGFuY2UiLCJleGVjdXRlQnJvd3NpbmciLCJxdWVyeSIsImJyb3dzaW5nQ29uZmlnIiwiYnJvd3NpbmdUeXBlIiwicmVmaW5lZFF1ZXJ5IiwiYnJvd3NpbmdfZW5hYmxlZCIsInN1Y2Nlc3MiLCJlcnJvciIsImJyb3dzaW5nX21vZGVscyIsImxlbmd0aCIsInNvcnRlZE1vZGVscyIsInNvcnQiLCJhIiwiYiIsIm9yZGVyIiwiY29uc29sZSIsImxvZyIsImxhc3RFcnJvciIsIm1vZGVsIiwicHJvdmlkZXIiLCJicm93c2luZ1Jlc3VsdCIsInBlcmZvcm1XZWJCcm93c2luZyIsIkVycm9yIiwiSlNPTiIsInN0cmluZ2lmeSIsImRhdGEiLCJzdWJzdHJpbmciLCJhaVJlc3VsdCIsInByb2Nlc3NXaXRoQUkiLCJjb250ZW50IiwibW9kZWxVc2VkIiwicHJvdmlkZXJVc2VkIiwiYnJvd3NpbmdEYXRhIiwiZXJyb3JNZXNzYWdlIiwibWVzc2FnZSIsIndhcm4iLCJyZXN1bHQiLCJzZWFyY2hBbmRFeHRyYWN0IiwidXJsTWF0Y2giLCJtYXRjaCIsInVybCIsIm5hdmlnYXRlQW5kRXh0cmFjdCIsImV4dHJhY3RVcmwiLCJvcmlnaW5hbFF1ZXJ5IiwicHJvbXB0IiwiYnVpbGRQcm9jZXNzaW5nUHJvbXB0IiwicmVzcG9uc2UiLCJjYWxsQUlQcm92aWRlciIsImRhdGFTdHIiLCJtZXNzYWdlcyIsInJvbGUiLCJhcGlVcmwiLCJoZWFkZXJzIiwiYm9keSIsImFwaV9rZXkiLCJ0ZW1wZXJhdHVyZSIsIm1heF90b2tlbnMiLCJmZXRjaCIsIm1ldGhvZCIsInNpZ25hbCIsIkFib3J0U2lnbmFsIiwidGltZW91dCIsIm9rIiwiZXJyb3JUZXh0IiwidGV4dCIsInN0YXR1cyIsImpzb24iLCJjaG9pY2VzIiwidHJpbSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ })

};
;