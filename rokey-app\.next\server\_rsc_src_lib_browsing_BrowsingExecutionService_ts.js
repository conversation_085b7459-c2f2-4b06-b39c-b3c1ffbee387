"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        const keysString = process.env.BROWSERLESS_API_KEYS;\n        if (!keysString) {\n            console.error('BROWSERLESS_API_KEYS not found in environment variables');\n            return;\n        }\n        this.apiKeys = keysString.split(',').map((key)=>key.trim()).filter(Boolean);\n        console.log(`Initialized Browserless service with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n        \n        // Wait for search results to load\n        await page.waitForSelector('${searchEngine === 'google' ? '[data-ved]' : '.b_algo'}', { timeout: 10000 });\n        \n        const results = await page.evaluate(() => {\n          const selector = '${searchEngine === 'google' ? '[data-ved] h3' : '.b_algo h2'}';\n          const elements = document.querySelectorAll(selector);\n          \n          return Array.from(elements).slice(0, 5).map(el => ({\n            title: el.textContent?.trim() || '',\n            link: el.closest('a')?.href || ''\n          }));\n        });\n        \n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2Jyb3dzZXJsZXNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSwrQ0FBK0M7QUFDL0MsMENBQTBDO0FBaUIxQyxNQUFNQTtJQVVKQyxhQUFjO2FBUk5DLFVBQW9CLEVBQUU7YUFDdEJDLGtCQUFrQjthQUNsQkMsZ0JBQXFDLElBQUlDO2FBQ3pDQyxZQUFpQyxJQUFJRDthQUM1QkUsY0FBYzthQUNkQyxrQkFBa0I7YUFDbEJDLFdBQVc7UUFHMUIsSUFBSSxDQUFDQyxjQUFjO0lBQ3JCO0lBRUEsT0FBT0MsY0FBa0M7UUFDdkMsSUFBSSxDQUFDWCxtQkFBbUJZLFFBQVEsRUFBRTtZQUNoQ1osbUJBQW1CWSxRQUFRLEdBQUcsSUFBSVo7UUFDcEM7UUFDQSxPQUFPQSxtQkFBbUJZLFFBQVE7SUFDcEM7SUFFUUYsaUJBQXVCO1FBQzdCLE1BQU1HLGFBQWFDLFFBQVFDLEdBQUcsQ0FBQ0Msb0JBQW9CO1FBQ25ELElBQUksQ0FBQ0gsWUFBWTtZQUNmSSxRQUFRQyxLQUFLLENBQUM7WUFDZDtRQUNGO1FBRUEsSUFBSSxDQUFDaEIsT0FBTyxHQUFHVyxXQUFXTSxLQUFLLENBQUMsS0FBS0MsR0FBRyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxJQUFJLElBQUlDLE1BQU0sQ0FBQ0M7UUFDbkVQLFFBQVFRLEdBQUcsQ0FBQyxDQUFDLHFDQUFxQyxFQUFFLElBQUksQ0FBQ3ZCLE9BQU8sQ0FBQ3dCLE1BQU0sQ0FBQyxTQUFTLENBQUM7UUFFbEYsNEJBQTRCO1FBQzVCLElBQUksQ0FBQ3hCLE9BQU8sQ0FBQ3lCLE9BQU8sQ0FBQ04sQ0FBQUE7WUFDbkIsSUFBSSxDQUFDakIsYUFBYSxDQUFDd0IsR0FBRyxDQUFDUCxLQUFLO1lBQzVCLElBQUksQ0FBQ2YsU0FBUyxDQUFDc0IsR0FBRyxDQUFDUCxLQUFLO1FBQzFCO0lBQ0Y7SUFFUVEsZ0JBQXdCO1FBQzlCLElBQUksSUFBSSxDQUFDM0IsT0FBTyxDQUFDd0IsTUFBTSxLQUFLLEdBQUc7WUFDN0IsTUFBTSxJQUFJSSxNQUFNO1FBQ2xCO1FBRUEsNENBQTRDO1FBQzVDLElBQUlDLFVBQVUsSUFBSSxDQUFDN0IsT0FBTyxDQUFDLEVBQUU7UUFDN0IsSUFBSThCLFlBQVksSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ0Y7UUFFdkMsS0FBSyxNQUFNVixPQUFPLElBQUksQ0FBQ25CLE9BQU8sQ0FBRTtZQUM5QixNQUFNZ0MsUUFBUSxJQUFJLENBQUNELGlCQUFpQixDQUFDWjtZQUNyQyxJQUFJYSxRQUFRRixXQUFXO2dCQUNyQkQsVUFBVVY7Z0JBQ1ZXLFlBQVlFO1lBQ2Q7UUFDRjtRQUVBLE9BQU9IO0lBQ1Q7SUFFUUUsa0JBQWtCWixHQUFXLEVBQVU7UUFDN0MsTUFBTWMsUUFBUSxJQUFJLENBQUMvQixhQUFhLENBQUNnQyxHQUFHLENBQUNmLFFBQVE7UUFDN0MsTUFBTWdCLFNBQVMsSUFBSSxDQUFDL0IsU0FBUyxDQUFDOEIsR0FBRyxDQUFDZixRQUFRO1FBQzFDLHNEQUFzRDtRQUN0RCxPQUFPYyxRQUFTRSxTQUFTO0lBQzNCO0lBRVFDLGtCQUFrQmpCLEdBQVcsRUFBUTtRQUMzQyxNQUFNa0IsZUFBZSxJQUFJLENBQUNuQyxhQUFhLENBQUNnQyxHQUFHLENBQUNmLFFBQVE7UUFDcEQsSUFBSSxDQUFDakIsYUFBYSxDQUFDd0IsR0FBRyxDQUFDUCxLQUFLa0IsZUFBZTtJQUM3QztJQUVRQyxrQkFBa0JuQixHQUFXLEVBQVE7UUFDM0MsTUFBTW9CLGdCQUFnQixJQUFJLENBQUNuQyxTQUFTLENBQUM4QixHQUFHLENBQUNmLFFBQVE7UUFDakQsSUFBSSxDQUFDZixTQUFTLENBQUNzQixHQUFHLENBQUNQLEtBQUtvQixnQkFBZ0I7SUFDMUM7SUFFUUMsYUFBYXJCLEdBQVcsRUFBVztRQUN6QyxNQUFNZ0IsU0FBUyxJQUFJLENBQUMvQixTQUFTLENBQUM4QixHQUFHLENBQUNmLFFBQVE7UUFDMUMsT0FBT2dCLFNBQVMsSUFBSSxDQUFDN0IsZUFBZTtJQUN0QztJQUVRbUMsaUJBQTJCO1FBQ2pDLE9BQU8sSUFBSSxDQUFDekMsT0FBTyxDQUFDcUIsTUFBTSxDQUFDRixDQUFBQSxNQUFPLElBQUksQ0FBQ3FCLFlBQVksQ0FBQ3JCO0lBQ3REO0lBRUEsTUFBTXVCLGdCQUNKQyxJQUFZLEVBQ1pDLE9BQWEsRUFDYkMsTUFBMEIsRUFDSTtRQUM5QixNQUFNQyxjQUFjLElBQUksQ0FBQ0wsY0FBYztRQUV2QyxJQUFJSyxZQUFZdEIsTUFBTSxLQUFLLEdBQUc7WUFDNUIsK0NBQStDO1lBQy9DLElBQUksQ0FBQ3BCLFNBQVMsQ0FBQzJDLEtBQUs7WUFDcEIsSUFBSSxDQUFDL0MsT0FBTyxDQUFDeUIsT0FBTyxDQUFDTixDQUFBQSxNQUFPLElBQUksQ0FBQ2YsU0FBUyxDQUFDc0IsR0FBRyxDQUFDUCxLQUFLO1lBQ3BESixRQUFRUSxHQUFHLENBQUM7UUFDZDtRQUVBLElBQUl5QixZQUEwQjtRQUU5QixJQUFLLElBQUlDLFVBQVUsR0FBR0EsVUFBVSxJQUFJLENBQUM1QyxXQUFXLEVBQUU0QyxVQUFXO1lBQzNELElBQUk7Z0JBQ0YsTUFBTUMsU0FBUyxJQUFJLENBQUN2QixhQUFhO2dCQUNqQyxJQUFJLENBQUNTLGlCQUFpQixDQUFDYztnQkFFdkIsTUFBTUMsV0FBVyxNQUFNLElBQUksQ0FBQ0MsV0FBVyxDQUFDRixRQUFRUCxNQUFNQyxTQUFTQztnQkFFL0QsZ0NBQWdDO2dCQUNoQyxPQUFPTTtZQUNULEVBQUUsT0FBT25DLE9BQU87Z0JBQ2RnQyxZQUFZaEM7Z0JBQ1pELFFBQVFDLEtBQUssQ0FBQyxDQUFDLG9CQUFvQixFQUFFaUMsVUFBVSxFQUFFLFFBQVEsQ0FBQyxFQUFFakM7Z0JBRTVELHVFQUF1RTtnQkFDdkUsSUFBSSxJQUFJLENBQUNxQyxnQkFBZ0IsQ0FBQ3JDLFFBQWlCO29CQUN6QyxNQUFNc0MsYUFBYSxJQUFJLENBQUMzQixhQUFhO29CQUNyQyxJQUFJLENBQUNXLGlCQUFpQixDQUFDZ0I7Z0JBQ3pCO1lBQ0Y7UUFDRjtRQUVBLE1BQU1OLGFBQWEsSUFBSXBCLE1BQU07SUFDL0I7SUFFQSxNQUFjd0IsWUFDWkYsTUFBYyxFQUNkUCxJQUFZLEVBQ1pDLE9BQWEsRUFDYkMsTUFBMEIsRUFDSTtRQUM5QixNQUFNVSxNQUFNLEdBQUcsSUFBSSxDQUFDaEQsUUFBUSxDQUFDLGdCQUFnQixFQUFFMkMsUUFBUTtRQUV2RCxNQUFNTSxjQUFjWixVQUFVO1lBQzVCRDtZQUNBQztRQUNGLElBQUlEO1FBRUosTUFBTWMsVUFBVTtZQUNkLGdCQUFnQmIsVUFBVSxxQkFBcUI7WUFDL0MsY0FBY0MsUUFBUWEsYUFBYTtRQUNyQztRQUVBLE1BQU1QLFdBQVcsTUFBTVEsTUFBTUosS0FBSztZQUNoQ0ssUUFBUTtZQUNSSDtZQUNBSSxNQUFNakIsVUFBVWtCLEtBQUtDLFNBQVMsQ0FBQ1AsZUFBZWI7WUFDOUNxQixRQUFRQyxZQUFZQyxPQUFPLENBQUNyQixRQUFRcUIsV0FBVztRQUNqRDtRQUVBLElBQUksQ0FBQ2YsU0FBU2dCLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1qQixTQUFTa0IsSUFBSTtZQUNyQyxNQUFNLElBQUl6QyxNQUFNLENBQUMsdUJBQXVCLEVBQUV1QixTQUFTbUIsTUFBTSxDQUFDLEdBQUcsRUFBRUYsV0FBVztRQUM1RTtRQUVBLE1BQU1HLFNBQVMsTUFBTXBCLFNBQVNxQixJQUFJO1FBQ2xDLE9BQU9EO0lBQ1Q7SUFFUWxCLGlCQUFpQnJDLEtBQVksRUFBVztRQUM5QyxNQUFNeUQsVUFBVXpELE1BQU15RCxPQUFPLENBQUNDLFdBQVc7UUFDekMsT0FBT0QsUUFBUUUsUUFBUSxDQUFDLGlCQUNqQkYsUUFBUUUsUUFBUSxDQUFDLFlBQ2pCRixRQUFRRSxRQUFRLENBQUMsVUFDakJGLFFBQVFFLFFBQVEsQ0FBQztJQUMxQjtJQUVBLCtDQUErQztJQUMvQyxNQUFNQyxtQkFBbUJyQixHQUFXLEVBQUVzQixRQUFpQixFQUFnQjtRQUNyRSxNQUFNbEMsT0FBTyxDQUFDOzt5QkFFTyxFQUFFWSxJQUFJOzs7d0JBR1AsRUFBRXNCLFdBQ2hCLENBQUMsa0JBQWtCLEVBQUVBLFNBQVMsd0NBQXdDLENBQUMsR0FDdkUscURBQ0Q7Ozs7a0JBSVMsRUFBRXRCLElBQUk7Ozs7Ozs7SUFPcEIsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDYixlQUFlLENBQUNDO0lBQzlCO0lBRUEsTUFBTW1DLGlCQUFpQkMsS0FBYSxFQUFFQyxlQUF1QixRQUFRLEVBQWdCO1FBQ25GLE1BQU1DLFlBQVlELGlCQUFpQixXQUMvQixDQUFDLGdDQUFnQyxFQUFFRSxtQkFBbUJILFFBQVEsR0FDOUQsQ0FBQyw4QkFBOEIsRUFBRUcsbUJBQW1CSCxRQUFRO1FBRWhFLE1BQU1wQyxPQUFPLENBQUM7O3lCQUVPLEVBQUVzQyxVQUFVOzs7b0NBR0QsRUFBRUQsaUJBQWlCLFdBQVcsZUFBZSxVQUFVOzs7NEJBRy9ELEVBQUVBLGlCQUFpQixXQUFXLGtCQUFrQixhQUFhOzs7Ozs7Ozs7OztvQkFXckUsRUFBRUQsTUFBTTsyQkFDRCxFQUFFQyxhQUFhOzs7Ozs7SUFNdEMsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDdEMsZUFBZSxDQUFDQztJQUM5QjtJQUVBLE1BQU13QyxlQUFlNUIsR0FBVyxFQUFFNkIsT0FJakMsRUFBZ0I7UUFDZixNQUFNQyxXQUFXRCxTQUFTQyxZQUFZO1FBQ3RDLE1BQU1SLFdBQVdPLFNBQVNQLFlBQVk7UUFDdEMsTUFBTVMsVUFBVUYsU0FBU0UsV0FBVztRQUVwQyxNQUFNM0MsT0FBTyxDQUFDOzt5QkFFTyxFQUFFWSxJQUFJOzs7YUFHbEIsRUFBRXNCLFNBQVM7O3NEQUU4QixFQUFFQSxTQUFTOzs7Ozs7Ozs7c0JBUzNDLEVBQUVRLFNBQVM7O3FCQUVaLEVBQUVDLFFBQVE7Ozs7OztrQkFNYixFQUFFL0IsSUFBSTs7dUJBRUQsRUFBRXNCLFNBQVM7c0JBQ1osRUFBRVEsU0FBUzs7Ozs7O0lBTTdCLENBQUM7UUFFRCxPQUFPLElBQUksQ0FBQzNDLGVBQWUsQ0FBQ0M7SUFDOUI7SUFFQTs7R0FFQyxHQUNELE1BQU00QyxTQUFTaEMsR0FBVyxFQUFFaUMsUUFBNkIsRUFBRUosT0FJMUQsRUFBZ0I7UUFDZixNQUFNSyxrQkFBa0JMLFNBQVNLLG1CQUFtQjtRQUNwRCxNQUFNQyxvQkFBb0JOLFNBQVNNLHFCQUFxQjtRQUN4RCxNQUFNQyxlQUFlUCxTQUFTTyxnQkFBZ0I7UUFFOUMsTUFBTWhELE9BQU8sQ0FBQzs7eUJBRU8sRUFBRVksSUFBSTs7eUJBRU4sRUFBRU8sS0FBS0MsU0FBUyxDQUFDeUIsVUFBVTs7OztvQ0FJaEIsRUFBRUcsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBa0V2QyxFQUFFRixnQkFBZ0I7Ozs7Y0FJaEIsRUFBRUMsb0JBQW9CLHlFQUF5RSw4QkFBOEI7Ozs7Ozs7Ozs7OztrQkFZekgsRUFBRW5DLElBQUk7Ozs7Ozs7O0lBUXBCLENBQUM7UUFFRCxPQUFPLElBQUksQ0FBQ2IsZUFBZSxDQUFDQztJQUM5QjtJQUVBOztHQUVDLEdBQ0QsTUFBTWlELGFBQWFyQyxHQUFXLEVBQUVzQyxjQUFpRCxXQUFXLEVBQWdCO1FBQzFHLE1BQU1sRCxPQUFPLENBQUM7O3lCQUVPLEVBQUVZLElBQUk7OzZCQUVGLEVBQUVzQyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tCQWdFekIsRUFBRXRDLElBQUk7Ozs7Ozs7SUFPcEIsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDYixlQUFlLENBQUNDO0lBQzlCO0lBRUE7O0dBRUMsR0FDRCxNQUFNbUQsc0JBQXNCdkMsR0FBVyxFQUFFd0MsTUFBYyxFQUFFWCxPQUl4RCxFQUFnQjtRQUNmLE1BQU1ZLGtCQUFrQlosU0FBU1ksbUJBQW1CO1FBQ3BELE1BQU05QixVQUFVa0IsU0FBU2xCLFdBQVc7UUFDcEMsTUFBTStCLGFBQWFiLFNBQVNhLGNBQWM7UUFFMUMsTUFBTXRELE9BQU8sQ0FBQzs7eUJBRU8sRUFBRVksSUFBSTs7UUFFdkIsRUFBRXlDLGtCQUFrQixDQUFDLDRCQUE0QixFQUFFQSxnQkFBZ0IsY0FBYyxFQUFFOUIsUUFBUSxJQUFJLENBQUMsR0FBRyxHQUFHOzs7O1VBSXBHLEVBQUU2QixPQUFPOzs7OzthQUtOLEVBQUVFLFdBQVc7Ozs7Ozs7Ozs7Ozs7a0JBYVIsRUFBRTFDLElBQUk7O3lCQUVDLEVBQUUwQyxXQUFXOzs7Ozs7SUFNbEMsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDdkQsZUFBZSxDQUFDQztJQUM5QjtJQUVBOztHQUVDLEdBQ0QsTUFBTXVELGFBQWEzQyxHQUFXLEVBQUU0QyxlQUF5QixFQUFnQjtRQUN2RSxNQUFNeEQsT0FBTyxDQUFDOzt5QkFFTyxFQUFFWSxJQUFJOztzQkFFVCxFQUFFTyxLQUFLQyxTQUFTLENBQUNvQyxpQkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQkEySHRDLEVBQUU1QyxJQUFJOzs7Ozs7OztJQVFwQixDQUFDO1FBRUQsT0FBTyxJQUFJLENBQUNiLGVBQWUsQ0FBQ0M7SUFDOUI7SUFFQSx5QkFBeUI7SUFDekJ5RCxXQUFnQjtRQUNkLE9BQU87WUFDTEMsV0FBVyxJQUFJLENBQUNyRyxPQUFPLENBQUN3QixNQUFNO1lBQzlCc0IsYUFBYSxJQUFJLENBQUNMLGNBQWMsR0FBR2pCLE1BQU07WUFDekM4RSxVQUFVQyxPQUFPQyxXQUFXLENBQUMsSUFBSSxDQUFDdEcsYUFBYTtZQUMvQ0UsV0FBV21HLE9BQU9DLFdBQVcsQ0FBQyxJQUFJLENBQUNwRyxTQUFTO1FBQzlDO0lBQ0Y7QUFDRjtBQUVBLGlFQUFlTixrQkFBa0JBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxsaWJcXGJyb3dzZXJsZXNzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEJyb3dzZXJsZXNzLmlvIEFQSSBzZXJ2aWNlIHdpdGgga2V5IHJvdGF0aW9uXG4vLyBTaW1pbGFyIHRvIEppbmEgQVBJIGtleSByb3RhdGlvbiBzeXN0ZW1cblxuaW50ZXJmYWNlIEJyb3dzZXJsZXNzUmVzcG9uc2Uge1xuICBkYXRhOiBhbnk7XG4gIHR5cGU6IHN0cmluZztcbiAgZXJyb3I/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBCcm93c2VybGVzc0NvbmZpZyB7XG4gIHRpbWVvdXQ/OiBudW1iZXI7XG4gIHZpZXdwb3J0Pzoge1xuICAgIHdpZHRoOiBudW1iZXI7XG4gICAgaGVpZ2h0OiBudW1iZXI7XG4gIH07XG4gIHVzZXJBZ2VudD86IHN0cmluZztcbn1cblxuY2xhc3MgQnJvd3Nlcmxlc3NTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IEJyb3dzZXJsZXNzU2VydmljZTtcbiAgcHJpdmF0ZSBhcGlLZXlzOiBzdHJpbmdbXSA9IFtdO1xuICBwcml2YXRlIGN1cnJlbnRLZXlJbmRleCA9IDA7XG4gIHByaXZhdGUga2V5VXNhZ2VDb3VudDogTWFwPHN0cmluZywgbnVtYmVyPiA9IG5ldyBNYXAoKTtcbiAgcHJpdmF0ZSBrZXlFcnJvcnM6IE1hcDxzdHJpbmcsIG51bWJlcj4gPSBuZXcgTWFwKCk7XG4gIHByaXZhdGUgcmVhZG9ubHkgTUFYX1JFVFJJRVMgPSAzO1xuICBwcml2YXRlIHJlYWRvbmx5IEVSUk9SX1RIUkVTSE9MRCA9IDU7XG4gIHByaXZhdGUgcmVhZG9ubHkgRU5EUE9JTlQgPSAnaHR0cHM6Ly9wcm9kdWN0aW9uLXNmby5icm93c2VybGVzcy5pbyc7XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5pbml0aWFsaXplS2V5cygpO1xuICB9XG5cbiAgc3RhdGljIGdldEluc3RhbmNlKCk6IEJyb3dzZXJsZXNzU2VydmljZSB7XG4gICAgaWYgKCFCcm93c2VybGVzc1NlcnZpY2UuaW5zdGFuY2UpIHtcbiAgICAgIEJyb3dzZXJsZXNzU2VydmljZS5pbnN0YW5jZSA9IG5ldyBCcm93c2VybGVzc1NlcnZpY2UoKTtcbiAgICB9XG4gICAgcmV0dXJuIEJyb3dzZXJsZXNzU2VydmljZS5pbnN0YW5jZTtcbiAgfVxuXG4gIHByaXZhdGUgaW5pdGlhbGl6ZUtleXMoKTogdm9pZCB7XG4gICAgY29uc3Qga2V5c1N0cmluZyA9IHByb2Nlc3MuZW52LkJST1dTRVJMRVNTX0FQSV9LRVlTO1xuICAgIGlmICgha2V5c1N0cmluZykge1xuICAgICAgY29uc29sZS5lcnJvcignQlJPV1NFUkxFU1NfQVBJX0tFWVMgbm90IGZvdW5kIGluIGVudmlyb25tZW50IHZhcmlhYmxlcycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRoaXMuYXBpS2V5cyA9IGtleXNTdHJpbmcuc3BsaXQoJywnKS5tYXAoa2V5ID0+IGtleS50cmltKCkpLmZpbHRlcihCb29sZWFuKTtcbiAgICBjb25zb2xlLmxvZyhgSW5pdGlhbGl6ZWQgQnJvd3Nlcmxlc3Mgc2VydmljZSB3aXRoICR7dGhpcy5hcGlLZXlzLmxlbmd0aH0gQVBJIGtleXNgKTtcblxuICAgIC8vIEluaXRpYWxpemUgdXNhZ2UgdHJhY2tpbmdcbiAgICB0aGlzLmFwaUtleXMuZm9yRWFjaChrZXkgPT4ge1xuICAgICAgdGhpcy5rZXlVc2FnZUNvdW50LnNldChrZXksIDApO1xuICAgICAgdGhpcy5rZXlFcnJvcnMuc2V0KGtleSwgMCk7XG4gICAgfSk7XG4gIH1cblxuICBwcml2YXRlIGdldE5leHRBcGlLZXkoKTogc3RyaW5nIHtcbiAgICBpZiAodGhpcy5hcGlLZXlzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBCcm93c2VybGVzcyBBUEkga2V5cyBhdmFpbGFibGUnKTtcbiAgICB9XG5cbiAgICAvLyBGaW5kIHRoZSBrZXkgd2l0aCBsb3dlc3QgdXNhZ2UgYW5kIGVycm9yc1xuICAgIGxldCBiZXN0S2V5ID0gdGhpcy5hcGlLZXlzWzBdO1xuICAgIGxldCBiZXN0U2NvcmUgPSB0aGlzLmNhbGN1bGF0ZUtleVNjb3JlKGJlc3RLZXkpO1xuXG4gICAgZm9yIChjb25zdCBrZXkgb2YgdGhpcy5hcGlLZXlzKSB7XG4gICAgICBjb25zdCBzY29yZSA9IHRoaXMuY2FsY3VsYXRlS2V5U2NvcmUoa2V5KTtcbiAgICAgIGlmIChzY29yZSA8IGJlc3RTY29yZSkge1xuICAgICAgICBiZXN0S2V5ID0ga2V5O1xuICAgICAgICBiZXN0U2NvcmUgPSBzY29yZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gYmVzdEtleTtcbiAgfVxuXG4gIHByaXZhdGUgY2FsY3VsYXRlS2V5U2NvcmUoa2V5OiBzdHJpbmcpOiBudW1iZXIge1xuICAgIGNvbnN0IHVzYWdlID0gdGhpcy5rZXlVc2FnZUNvdW50LmdldChrZXkpIHx8IDA7XG4gICAgY29uc3QgZXJyb3JzID0gdGhpcy5rZXlFcnJvcnMuZ2V0KGtleSkgfHwgMDtcbiAgICAvLyBIaWdoZXIgc2NvcmUgPSB3b3JzZSBrZXkgKG1vcmUgdXNhZ2UgKyBtb3JlIGVycm9ycylcbiAgICByZXR1cm4gdXNhZ2UgKyAoZXJyb3JzICogMTApO1xuICB9XG5cbiAgcHJpdmF0ZSBpbmNyZW1lbnRLZXlVc2FnZShrZXk6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IGN1cnJlbnRVc2FnZSA9IHRoaXMua2V5VXNhZ2VDb3VudC5nZXQoa2V5KSB8fCAwO1xuICAgIHRoaXMua2V5VXNhZ2VDb3VudC5zZXQoa2V5LCBjdXJyZW50VXNhZ2UgKyAxKTtcbiAgfVxuXG4gIHByaXZhdGUgaW5jcmVtZW50S2V5RXJyb3Ioa2V5OiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBjdXJyZW50RXJyb3JzID0gdGhpcy5rZXlFcnJvcnMuZ2V0KGtleSkgfHwgMDtcbiAgICB0aGlzLmtleUVycm9ycy5zZXQoa2V5LCBjdXJyZW50RXJyb3JzICsgMSk7XG4gIH1cblxuICBwcml2YXRlIGlzS2V5SGVhbHRoeShrZXk6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IGVycm9ycyA9IHRoaXMua2V5RXJyb3JzLmdldChrZXkpIHx8IDA7XG4gICAgcmV0dXJuIGVycm9ycyA8IHRoaXMuRVJST1JfVEhSRVNIT0xEO1xuICB9XG5cbiAgcHJpdmF0ZSBnZXRIZWFsdGh5S2V5cygpOiBzdHJpbmdbXSB7XG4gICAgcmV0dXJuIHRoaXMuYXBpS2V5cy5maWx0ZXIoa2V5ID0+IHRoaXMuaXNLZXlIZWFsdGh5KGtleSkpO1xuICB9XG5cbiAgYXN5bmMgZXhlY3V0ZUZ1bmN0aW9uKFxuICAgIGNvZGU6IHN0cmluZyxcbiAgICBjb250ZXh0PzogYW55LFxuICAgIGNvbmZpZz86IEJyb3dzZXJsZXNzQ29uZmlnXG4gICk6IFByb21pc2U8QnJvd3Nlcmxlc3NSZXNwb25zZT4ge1xuICAgIGNvbnN0IGhlYWx0aHlLZXlzID0gdGhpcy5nZXRIZWFsdGh5S2V5cygpO1xuICAgIFxuICAgIGlmIChoZWFsdGh5S2V5cy5sZW5ndGggPT09IDApIHtcbiAgICAgIC8vIFJlc2V0IGVycm9yIGNvdW50cyBpZiBhbGwga2V5cyBhcmUgdW5oZWFsdGh5XG4gICAgICB0aGlzLmtleUVycm9ycy5jbGVhcigpO1xuICAgICAgdGhpcy5hcGlLZXlzLmZvckVhY2goa2V5ID0+IHRoaXMua2V5RXJyb3JzLnNldChrZXksIDApKTtcbiAgICAgIGNvbnNvbGUubG9nKCdBbGwgQnJvd3Nlcmxlc3Mga2V5cyB3ZXJlIHVuaGVhbHRoeSwgcmVzZXR0aW5nIGVycm9yIGNvdW50cycpO1xuICAgIH1cblxuICAgIGxldCBsYXN0RXJyb3I6IEVycm9yIHwgbnVsbCA9IG51bGw7XG4gICAgXG4gICAgZm9yIChsZXQgYXR0ZW1wdCA9IDA7IGF0dGVtcHQgPCB0aGlzLk1BWF9SRVRSSUVTOyBhdHRlbXB0KyspIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGFwaUtleSA9IHRoaXMuZ2V0TmV4dEFwaUtleSgpO1xuICAgICAgICB0aGlzLmluY3JlbWVudEtleVVzYWdlKGFwaUtleSk7XG5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLm1ha2VSZXF1ZXN0KGFwaUtleSwgY29kZSwgY29udGV4dCwgY29uZmlnKTtcbiAgICAgICAgXG4gICAgICAgIC8vIFN1Y2Nlc3MgLSByZXR1cm4gdGhlIHJlc3BvbnNlXG4gICAgICAgIHJldHVybiByZXNwb25zZTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGxhc3RFcnJvciA9IGVycm9yIGFzIEVycm9yO1xuICAgICAgICBjb25zb2xlLmVycm9yKGBCcm93c2VybGVzcyBhdHRlbXB0ICR7YXR0ZW1wdCArIDF9IGZhaWxlZDpgLCBlcnJvcik7XG4gICAgICAgIFxuICAgICAgICAvLyBJZiBpdCdzIGEgcmF0ZSBsaW1pdCBvciBxdW90YSBlcnJvciwgbWFyayB0aGUga2V5IGFzIGhhdmluZyBhbiBlcnJvclxuICAgICAgICBpZiAodGhpcy5pc1JhdGVMaW1pdEVycm9yKGVycm9yIGFzIEVycm9yKSkge1xuICAgICAgICAgIGNvbnN0IGN1cnJlbnRLZXkgPSB0aGlzLmdldE5leHRBcGlLZXkoKTtcbiAgICAgICAgICB0aGlzLmluY3JlbWVudEtleUVycm9yKGN1cnJlbnRLZXkpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgdGhyb3cgbGFzdEVycm9yIHx8IG5ldyBFcnJvcignQWxsIEJyb3dzZXJsZXNzIEFQSSBhdHRlbXB0cyBmYWlsZWQnKTtcbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgbWFrZVJlcXVlc3QoXG4gICAgYXBpS2V5OiBzdHJpbmcsXG4gICAgY29kZTogc3RyaW5nLFxuICAgIGNvbnRleHQ/OiBhbnksXG4gICAgY29uZmlnPzogQnJvd3Nlcmxlc3NDb25maWdcbiAgKTogUHJvbWlzZTxCcm93c2VybGVzc1Jlc3BvbnNlPiB7XG4gICAgY29uc3QgdXJsID0gYCR7dGhpcy5FTkRQT0lOVH0vZnVuY3Rpb24/dG9rZW49JHthcGlLZXl9YDtcbiAgICBcbiAgICBjb25zdCByZXF1ZXN0Qm9keSA9IGNvbnRleHQgPyB7XG4gICAgICBjb2RlLFxuICAgICAgY29udGV4dFxuICAgIH0gOiBjb2RlO1xuXG4gICAgY29uc3QgaGVhZGVycyA9IHtcbiAgICAgICdDb250ZW50LVR5cGUnOiBjb250ZXh0ID8gJ2FwcGxpY2F0aW9uL2pzb24nIDogJ2FwcGxpY2F0aW9uL2phdmFzY3JpcHQnLFxuICAgICAgJ1VzZXItQWdlbnQnOiBjb25maWc/LnVzZXJBZ2VudCB8fCAnUm91S2V5LUJyb3dzZXItQWdlbnQvMS4wJ1xuICAgIH07XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzLFxuICAgICAgYm9keTogY29udGV4dCA/IEpTT04uc3RyaW5naWZ5KHJlcXVlc3RCb2R5KSA6IGNvZGUsXG4gICAgICBzaWduYWw6IEFib3J0U2lnbmFsLnRpbWVvdXQoY29uZmlnPy50aW1lb3V0IHx8IDMwMDAwKVxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBCcm93c2VybGVzcyBBUEkgZXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfSAtICR7ZXJyb3JUZXh0fWApO1xuICAgIH1cblxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG5cbiAgcHJpdmF0ZSBpc1JhdGVMaW1pdEVycm9yKGVycm9yOiBFcnJvcik6IGJvb2xlYW4ge1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlLnRvTG93ZXJDYXNlKCk7XG4gICAgcmV0dXJuIG1lc3NhZ2UuaW5jbHVkZXMoJ3JhdGUgbGltaXQnKSB8fCBcbiAgICAgICAgICAgbWVzc2FnZS5pbmNsdWRlcygncXVvdGEnKSB8fCBcbiAgICAgICAgICAgbWVzc2FnZS5pbmNsdWRlcygnNDI5JykgfHxcbiAgICAgICAgICAgbWVzc2FnZS5pbmNsdWRlcygndG9vIG1hbnkgcmVxdWVzdHMnKTtcbiAgfVxuXG4gIC8vIENvbnZlbmllbmNlIG1ldGhvZHMgZm9yIGNvbW1vbiBicm93c2VyIHRhc2tzXG4gIGFzeW5jIG5hdmlnYXRlQW5kRXh0cmFjdCh1cmw6IHN0cmluZywgc2VsZWN0b3I/OiBzdHJpbmcpOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IGNvZGUgPSBgXG4gICAgICBleHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiAoeyBwYWdlIH0pIHtcbiAgICAgICAgYXdhaXQgcGFnZS5nb3RvKFwiJHt1cmx9XCIsIHsgd2FpdFVudGlsOiAnbmV0d29ya2lkbGUwJyB9KTtcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IHRpdGxlID0gYXdhaXQgcGFnZS50aXRsZSgpO1xuICAgICAgICBjb25zdCBjb250ZW50ID0gJHtzZWxlY3RvciA/IFxuICAgICAgICAgIGBhd2FpdCBwYWdlLiRldmFsKFwiJHtzZWxlY3Rvcn1cIiwgZWwgPT4gZWwudGV4dENvbnRlbnQgfHwgZWwuaW5uZXJUZXh0KWAgOiBcbiAgICAgICAgICAnYXdhaXQgcGFnZS5ldmFsdWF0ZSgoKSA9PiBkb2N1bWVudC5ib2R5LmlubmVyVGV4dCknXG4gICAgICAgIH07XG4gICAgICAgIFxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIHVybDogXCIke3VybH1cIixcbiAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgY29udGVudDogY29udGVudD8udHJpbSgpIHx8IFwiXCJcbiAgICAgICAgICB9LFxuICAgICAgICAgIHR5cGU6IFwiYXBwbGljYXRpb24vanNvblwiXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgYDtcblxuICAgIHJldHVybiB0aGlzLmV4ZWN1dGVGdW5jdGlvbihjb2RlKTtcbiAgfVxuXG4gIGFzeW5jIHNlYXJjaEFuZEV4dHJhY3QocXVlcnk6IHN0cmluZywgc2VhcmNoRW5naW5lOiBzdHJpbmcgPSAnZ29vZ2xlJyk6IFByb21pc2U8YW55PiB7XG4gICAgY29uc3Qgc2VhcmNoVXJsID0gc2VhcmNoRW5naW5lID09PSAnZ29vZ2xlJyBcbiAgICAgID8gYGh0dHBzOi8vd3d3Lmdvb2dsZS5jb20vc2VhcmNoP3E9JHtlbmNvZGVVUklDb21wb25lbnQocXVlcnkpfWBcbiAgICAgIDogYGh0dHBzOi8vd3d3LmJpbmcuY29tL3NlYXJjaD9xPSR7ZW5jb2RlVVJJQ29tcG9uZW50KHF1ZXJ5KX1gO1xuXG4gICAgY29uc3QgY29kZSA9IGBcbiAgICAgIGV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uICh7IHBhZ2UgfSkge1xuICAgICAgICBhd2FpdCBwYWdlLmdvdG8oXCIke3NlYXJjaFVybH1cIiwgeyB3YWl0VW50aWw6ICduZXR3b3JraWRsZTAnIH0pO1xuICAgICAgICBcbiAgICAgICAgLy8gV2FpdCBmb3Igc2VhcmNoIHJlc3VsdHMgdG8gbG9hZFxuICAgICAgICBhd2FpdCBwYWdlLndhaXRGb3JTZWxlY3RvcignJHtzZWFyY2hFbmdpbmUgPT09ICdnb29nbGUnID8gJ1tkYXRhLXZlZF0nIDogJy5iX2FsZ28nfScsIHsgdGltZW91dDogMTAwMDAgfSk7XG4gICAgICAgIFxuICAgICAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgcGFnZS5ldmFsdWF0ZSgoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc2VsZWN0b3IgPSAnJHtzZWFyY2hFbmdpbmUgPT09ICdnb29nbGUnID8gJ1tkYXRhLXZlZF0gaDMnIDogJy5iX2FsZ28gaDInfSc7XG4gICAgICAgICAgY29uc3QgZWxlbWVudHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKHNlbGVjdG9yKTtcbiAgICAgICAgICBcbiAgICAgICAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50cykuc2xpY2UoMCwgNSkubWFwKGVsID0+ICh7XG4gICAgICAgICAgICB0aXRsZTogZWwudGV4dENvbnRlbnQ/LnRyaW0oKSB8fCAnJyxcbiAgICAgICAgICAgIGxpbms6IGVsLmNsb3Nlc3QoJ2EnKT8uaHJlZiB8fCAnJ1xuICAgICAgICAgIH0pKTtcbiAgICAgICAgfSk7XG4gICAgICAgIFxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIHF1ZXJ5OiBcIiR7cXVlcnl9XCIsXG4gICAgICAgICAgICBzZWFyY2hFbmdpbmU6IFwiJHtzZWFyY2hFbmdpbmV9XCIsXG4gICAgICAgICAgICByZXN1bHRzXG4gICAgICAgICAgfSxcbiAgICAgICAgICB0eXBlOiBcImFwcGxpY2F0aW9uL2pzb25cIlxuICAgICAgICB9O1xuICAgICAgfVxuICAgIGA7XG5cbiAgICByZXR1cm4gdGhpcy5leGVjdXRlRnVuY3Rpb24oY29kZSk7XG4gIH1cblxuICBhc3luYyB0YWtlU2NyZWVuc2hvdCh1cmw6IHN0cmluZywgb3B0aW9ucz86IHtcbiAgICBmdWxsUGFnZT86IGJvb2xlYW47XG4gICAgc2VsZWN0b3I/OiBzdHJpbmc7XG4gICAgcXVhbGl0eT86IG51bWJlcjtcbiAgfSk6IFByb21pc2U8YW55PiB7XG4gICAgY29uc3QgZnVsbFBhZ2UgPSBvcHRpb25zPy5mdWxsUGFnZSA/PyBmYWxzZTtcbiAgICBjb25zdCBzZWxlY3RvciA9IG9wdGlvbnM/LnNlbGVjdG9yIHx8ICcnO1xuICAgIGNvbnN0IHF1YWxpdHkgPSBvcHRpb25zPy5xdWFsaXR5IHx8IDgwO1xuXG4gICAgY29uc3QgY29kZSA9IGBcbiAgICAgIGV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uICh7IHBhZ2UgfSkge1xuICAgICAgICBhd2FpdCBwYWdlLmdvdG8oXCIke3VybH1cIiwgeyB3YWl0VW50aWw6ICduZXR3b3JraWRsZTAnIH0pO1xuXG4gICAgICAgIGxldCBzY3JlZW5zaG90O1xuICAgICAgICBpZiAoXCIke3NlbGVjdG9yfVwiKSB7XG4gICAgICAgICAgLy8gU2NyZWVuc2hvdCBzcGVjaWZpYyBlbGVtZW50XG4gICAgICAgICAgY29uc3QgZWxlbWVudCA9IGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKFwiJHtzZWxlY3Rvcn1cIiwgeyB0aW1lb3V0OiAxMDAwMCB9KTtcbiAgICAgICAgICBzY3JlZW5zaG90ID0gYXdhaXQgZWxlbWVudC5zY3JlZW5zaG90KHtcbiAgICAgICAgICAgIGVuY29kaW5nOiAnYmFzZTY0JyxcbiAgICAgICAgICAgIHR5cGU6ICdwbmcnXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gU2NyZWVuc2hvdCBmdWxsIHBhZ2Ugb3Igdmlld3BvcnRcbiAgICAgICAgICBzY3JlZW5zaG90ID0gYXdhaXQgcGFnZS5zY3JlZW5zaG90KHtcbiAgICAgICAgICAgIGVuY29kaW5nOiAnYmFzZTY0JyxcbiAgICAgICAgICAgIGZ1bGxQYWdlOiAke2Z1bGxQYWdlfSxcbiAgICAgICAgICAgIHR5cGU6ICdwbmcnLFxuICAgICAgICAgICAgcXVhbGl0eTogJHtxdWFsaXR5fVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICB1cmw6IFwiJHt1cmx9XCIsXG4gICAgICAgICAgICBzY3JlZW5zaG90OiBzY3JlZW5zaG90LFxuICAgICAgICAgICAgc2VsZWN0b3I6IFwiJHtzZWxlY3Rvcn1cIixcbiAgICAgICAgICAgIGZ1bGxQYWdlOiAke2Z1bGxQYWdlfSxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfSxcbiAgICAgICAgICB0eXBlOiBcImFwcGxpY2F0aW9uL2pzb25cIlxuICAgICAgICB9O1xuICAgICAgfVxuICAgIGA7XG5cbiAgICByZXR1cm4gdGhpcy5leGVjdXRlRnVuY3Rpb24oY29kZSk7XG4gIH1cblxuICAvKipcbiAgICogQWR2YW5jZWQgZm9ybSBmaWxsaW5nIHdpdGggaW50ZWxsaWdlbnQgZmllbGQgZGV0ZWN0aW9uXG4gICAqL1xuICBhc3luYyBmaWxsRm9ybSh1cmw6IHN0cmluZywgZm9ybURhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4sIG9wdGlvbnM/OiB7XG4gICAgc3VibWl0QWZ0ZXJGaWxsPzogYm9vbGVhbjtcbiAgICB3YWl0Rm9yTmF2aWdhdGlvbj86IGJvb2xlYW47XG4gICAgZm9ybVNlbGVjdG9yPzogc3RyaW5nO1xuICB9KTogUHJvbWlzZTxhbnk+IHtcbiAgICBjb25zdCBzdWJtaXRBZnRlckZpbGwgPSBvcHRpb25zPy5zdWJtaXRBZnRlckZpbGwgPz8gZmFsc2U7XG4gICAgY29uc3Qgd2FpdEZvck5hdmlnYXRpb24gPSBvcHRpb25zPy53YWl0Rm9yTmF2aWdhdGlvbiA/PyBmYWxzZTtcbiAgICBjb25zdCBmb3JtU2VsZWN0b3IgPSBvcHRpb25zPy5mb3JtU2VsZWN0b3IgfHwgJ2Zvcm0nO1xuXG4gICAgY29uc3QgY29kZSA9IGBcbiAgICAgIGV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uICh7IHBhZ2UgfSkge1xuICAgICAgICBhd2FpdCBwYWdlLmdvdG8oXCIke3VybH1cIiwgeyB3YWl0VW50aWw6ICduZXR3b3JraWRsZTAnIH0pO1xuXG4gICAgICAgIGNvbnN0IGZvcm1EYXRhID0gJHtKU09OLnN0cmluZ2lmeShmb3JtRGF0YSl9O1xuICAgICAgICBjb25zdCByZXN1bHRzID0gW107XG5cbiAgICAgICAgLy8gV2FpdCBmb3IgZm9ybSB0byBiZSBwcmVzZW50XG4gICAgICAgIGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKFwiJHtmb3JtU2VsZWN0b3J9XCIsIHsgdGltZW91dDogMTAwMDAgfSk7XG5cbiAgICAgICAgLy8gRmlsbCBlYWNoIGZpZWxkIGludGVsbGlnZW50bHlcbiAgICAgICAgZm9yIChjb25zdCBbZmllbGROYW1lLCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoZm9ybURhdGEpKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIFRyeSBtdWx0aXBsZSBzZWxlY3RvciBzdHJhdGVnaWVzXG4gICAgICAgICAgICBjb25zdCBzZWxlY3RvcnMgPSBbXG4gICAgICAgICAgICAgIFxcYGlucHV0W25hbWU9XCJcXCR7ZmllbGROYW1lfVwiXVxcYCxcbiAgICAgICAgICAgICAgXFxgaW5wdXRbaWQ9XCJcXCR7ZmllbGROYW1lfVwiXVxcYCxcbiAgICAgICAgICAgICAgXFxgdGV4dGFyZWFbbmFtZT1cIlxcJHtmaWVsZE5hbWV9XCJdXFxgLFxuICAgICAgICAgICAgICBcXGBzZWxlY3RbbmFtZT1cIlxcJHtmaWVsZE5hbWV9XCJdXFxgLFxuICAgICAgICAgICAgICBcXGBpbnB1dFtwbGFjZWhvbGRlcio9XCJcXCR7ZmllbGROYW1lfVwiXVxcYCxcbiAgICAgICAgICAgICAgXFxgaW5wdXRbYXJpYS1sYWJlbCo9XCJcXCR7ZmllbGROYW1lfVwiXVxcYCxcbiAgICAgICAgICAgICAgXFxgW2RhdGEtdGVzdGlkPVwiXFwke2ZpZWxkTmFtZX1cIl1cXGBcbiAgICAgICAgICAgIF07XG5cbiAgICAgICAgICAgIGxldCBmaWxsZWQgPSBmYWxzZTtcbiAgICAgICAgICAgIGZvciAoY29uc3Qgc2VsZWN0b3Igb2Ygc2VsZWN0b3JzKSB7XG4gICAgICAgICAgICAgIGNvbnN0IGVsZW1lbnRzID0gYXdhaXQgcGFnZS4kJChzZWxlY3Rvcik7XG4gICAgICAgICAgICAgIGlmIChlbGVtZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZWxlbWVudCA9IGVsZW1lbnRzWzBdO1xuICAgICAgICAgICAgICAgIGNvbnN0IHRhZ05hbWUgPSBhd2FpdCBlbGVtZW50LmV2YWx1YXRlKGVsID0+IGVsLnRhZ05hbWUudG9Mb3dlckNhc2UoKSk7XG5cbiAgICAgICAgICAgICAgICBpZiAodGFnTmFtZSA9PT0gJ3NlbGVjdCcpIHtcbiAgICAgICAgICAgICAgICAgIGF3YWl0IGVsZW1lbnQuc2VsZWN0T3B0aW9uKHZhbHVlLnRvU3RyaW5nKCkpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodGFnTmFtZSA9PT0gJ2lucHV0Jykge1xuICAgICAgICAgICAgICAgICAgY29uc3QgaW5wdXRUeXBlID0gYXdhaXQgZWxlbWVudC5nZXRBdHRyaWJ1dGUoJ3R5cGUnKTtcbiAgICAgICAgICAgICAgICAgIGlmIChpbnB1dFR5cGUgPT09ICdjaGVja2JveCcgfHwgaW5wdXRUeXBlID09PSAncmFkaW8nKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSkgYXdhaXQgZWxlbWVudC5jaGVjaygpO1xuICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgZWxlbWVudC5maWxsKHZhbHVlLnRvU3RyaW5nKCkpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICBhd2FpdCBlbGVtZW50LmZpbGwodmFsdWUudG9TdHJpbmcoKSk7XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgcmVzdWx0cy5wdXNoKHtcbiAgICAgICAgICAgICAgICAgIGZpZWxkOiBmaWVsZE5hbWUsXG4gICAgICAgICAgICAgICAgICBzZWxlY3Rvcjogc2VsZWN0b3IsXG4gICAgICAgICAgICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgZmlsbGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoIWZpbGxlZCkge1xuICAgICAgICAgICAgICByZXN1bHRzLnB1c2goe1xuICAgICAgICAgICAgICAgIGZpZWxkOiBmaWVsZE5hbWUsXG4gICAgICAgICAgICAgICAgdmFsdWU6IHZhbHVlLFxuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiAnRmllbGQgbm90IGZvdW5kJ1xuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgcmVzdWx0cy5wdXNoKHtcbiAgICAgICAgICAgICAgZmllbGQ6IGZpZWxkTmFtZSxcbiAgICAgICAgICAgICAgdmFsdWU6IHZhbHVlLFxuICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2VcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGxldCBzdWJtaXRSZXN1bHQgPSBudWxsO1xuICAgICAgICBpZiAoJHtzdWJtaXRBZnRlckZpbGx9KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHN1Ym1pdEJ1dHRvbiA9IGF3YWl0IHBhZ2UuJCgnaW5wdXRbdHlwZT1cInN1Ym1pdFwiXSwgYnV0dG9uW3R5cGU9XCJzdWJtaXRcIl0sIGJ1dHRvbjpoYXMtdGV4dChcIlN1Ym1pdFwiKScpO1xuICAgICAgICAgICAgaWYgKHN1Ym1pdEJ1dHRvbikge1xuICAgICAgICAgICAgICAke3dhaXRGb3JOYXZpZ2F0aW9uID8gJ2F3YWl0IFByb21pc2UuYWxsKFtwYWdlLndhaXRGb3JOYXZpZ2F0aW9uKCksIHN1Ym1pdEJ1dHRvbi5jbGljaygpXSk7JyA6ICdhd2FpdCBzdWJtaXRCdXR0b24uY2xpY2soKTsnfVxuICAgICAgICAgICAgICBzdWJtaXRSZXN1bHQgPSB7IHN1Y2Nlc3M6IHRydWUsIG1lc3NhZ2U6ICdGb3JtIHN1Ym1pdHRlZCBzdWNjZXNzZnVsbHknIH07XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICBzdWJtaXRSZXN1bHQgPSB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1N1Ym1pdCBidXR0b24gbm90IGZvdW5kJyB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBzdWJtaXRSZXN1bHQgPSB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3IubWVzc2FnZSB9O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgdXJsOiBcIiR7dXJsfVwiLFxuICAgICAgICAgICAgZm9ybUZpbGxSZXN1bHRzOiByZXN1bHRzLFxuICAgICAgICAgICAgc3VibWl0UmVzdWx0OiBzdWJtaXRSZXN1bHQsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH0sXG4gICAgICAgICAgdHlwZTogXCJhcHBsaWNhdGlvbi9qc29uXCJcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICBgO1xuXG4gICAgcmV0dXJuIHRoaXMuZXhlY3V0ZUZ1bmN0aW9uKGNvZGUpO1xuICB9XG5cbiAgLyoqXG4gICAqIENBUFRDSEEgc29sdmluZyB3aXRoIG11bHRpcGxlIHN0cmF0ZWdpZXNcbiAgICovXG4gIGFzeW5jIHNvbHZlQ2FwdGNoYSh1cmw6IHN0cmluZywgY2FwdGNoYVR5cGU6ICdyZWNhcHRjaGEnIHwgJ2hjYXB0Y2hhJyB8ICd0ZXh0JyA9ICdyZWNhcHRjaGEnKTogUHJvbWlzZTxhbnk+IHtcbiAgICBjb25zdCBjb2RlID0gYFxuICAgICAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gKHsgcGFnZSB9KSB7XG4gICAgICAgIGF3YWl0IHBhZ2UuZ290byhcIiR7dXJsfVwiLCB7IHdhaXRVbnRpbDogJ25ldHdvcmtpZGxlMCcgfSk7XG5cbiAgICAgICAgY29uc3QgY2FwdGNoYVR5cGUgPSBcIiR7Y2FwdGNoYVR5cGV9XCI7XG4gICAgICAgIGxldCByZXN1bHQgPSB7IHN1Y2Nlc3M6IGZhbHNlLCB0eXBlOiBjYXB0Y2hhVHlwZSB9O1xuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgaWYgKGNhcHRjaGFUeXBlID09PSAncmVjYXB0Y2hhJykge1xuICAgICAgICAgICAgLy8gTG9vayBmb3IgcmVDQVBUQ0hBXG4gICAgICAgICAgICBjb25zdCByZWNhcHRjaGEgPSBhd2FpdCBwYWdlLiQoJy5nLXJlY2FwdGNoYSwgW2RhdGEtc2l0ZWtleV0nKTtcbiAgICAgICAgICAgIGlmIChyZWNhcHRjaGEpIHtcbiAgICAgICAgICAgICAgLy8gRm9yIG5vdywgd2UnbGwgZGV0ZWN0IGFuZCByZXBvcnQgdGhlIHByZXNlbmNlXG4gICAgICAgICAgICAgIC8vIEluIHByb2R1Y3Rpb24sIGludGVncmF0ZSB3aXRoIDJjYXB0Y2hhIG9yIHNpbWlsYXIgc2VydmljZVxuICAgICAgICAgICAgICBjb25zdCBzaXRla2V5ID0gYXdhaXQgcmVjYXB0Y2hhLmdldEF0dHJpYnV0ZSgnZGF0YS1zaXRla2V5Jyk7XG4gICAgICAgICAgICAgIHJlc3VsdCA9IHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICB0eXBlOiAncmVjYXB0Y2hhJyxcbiAgICAgICAgICAgICAgICBkZXRlY3RlZDogdHJ1ZSxcbiAgICAgICAgICAgICAgICBzaXRla2V5OiBzaXRla2V5LFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdyZUNBUFRDSEEgZGV0ZWN0ZWQgYnV0IHNvbHZpbmcgbm90IGltcGxlbWVudGVkIHlldCdcbiAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGVsc2UgaWYgKGNhcHRjaGFUeXBlID09PSAnaGNhcHRjaGEnKSB7XG4gICAgICAgICAgICAvLyBMb29rIGZvciBoQ2FwdGNoYVxuICAgICAgICAgICAgY29uc3QgaGNhcHRjaGEgPSBhd2FpdCBwYWdlLiQoJy5oLWNhcHRjaGEsIFtkYXRhLWhjYXB0Y2hhLXNpdGVrZXldJyk7XG4gICAgICAgICAgICBpZiAoaGNhcHRjaGEpIHtcbiAgICAgICAgICAgICAgY29uc3Qgc2l0ZWtleSA9IGF3YWl0IGhjYXB0Y2hhLmdldEF0dHJpYnV0ZSgnZGF0YS1oY2FwdGNoYS1zaXRla2V5Jyk7XG4gICAgICAgICAgICAgIHJlc3VsdCA9IHtcbiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICB0eXBlOiAnaGNhcHRjaGEnLFxuICAgICAgICAgICAgICAgIGRldGVjdGVkOiB0cnVlLFxuICAgICAgICAgICAgICAgIHNpdGVrZXk6IHNpdGVrZXksXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogJ2hDYXB0Y2hhIGRldGVjdGVkIGJ1dCBzb2x2aW5nIG5vdCBpbXBsZW1lbnRlZCB5ZXQnXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIGlmIChjYXB0Y2hhVHlwZSA9PT0gJ3RleHQnKSB7XG4gICAgICAgICAgICAvLyBMb29rIGZvciB0ZXh0LWJhc2VkIENBUFRDSEFcbiAgICAgICAgICAgIGNvbnN0IHRleHRDYXB0Y2hhID0gYXdhaXQgcGFnZS4kKCdpbWdbc3JjKj1cImNhcHRjaGFcIl0sIGltZ1thbHQqPVwiY2FwdGNoYVwiXSwgLmNhcHRjaGEtaW1hZ2UnKTtcbiAgICAgICAgICAgIGlmICh0ZXh0Q2FwdGNoYSkge1xuICAgICAgICAgICAgICByZXN1bHQgPSB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLFxuICAgICAgICAgICAgICAgIGRldGVjdGVkOiB0cnVlLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdUZXh0IENBUFRDSEEgZGV0ZWN0ZWQgYnV0IHNvbHZpbmcgbm90IGltcGxlbWVudGVkIHlldCdcbiAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBJZiBubyBDQVBUQ0hBIGRldGVjdGVkXG4gICAgICAgICAgaWYgKCFyZXN1bHQuZGV0ZWN0ZWQpIHtcbiAgICAgICAgICAgIHJlc3VsdCA9IHtcbiAgICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgICAgdHlwZTogY2FwdGNoYVR5cGUsXG4gICAgICAgICAgICAgIGRldGVjdGVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgbWVzc2FnZTogJ05vIENBUFRDSEEgZGV0ZWN0ZWQgb24gcGFnZSdcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIHJlc3VsdCA9IHtcbiAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgdHlwZTogY2FwdGNoYVR5cGUsXG4gICAgICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZVxuICAgICAgICAgIH07XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIHVybDogXCIke3VybH1cIixcbiAgICAgICAgICAgIGNhcHRjaGFSZXN1bHQ6IHJlc3VsdCxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfSxcbiAgICAgICAgICB0eXBlOiBcImFwcGxpY2F0aW9uL2pzb25cIlxuICAgICAgICB9O1xuICAgICAgfVxuICAgIGA7XG5cbiAgICByZXR1cm4gdGhpcy5leGVjdXRlRnVuY3Rpb24oY29kZSk7XG4gIH1cblxuICAvKipcbiAgICogRXhlY3V0ZSBjdXN0b20gSmF2YVNjcmlwdCB3aXRoIGFkdmFuY2VkIGNhcGFiaWxpdGllc1xuICAgKi9cbiAgYXN5bmMgZXhlY3V0ZUFkdmFuY2VkU2NyaXB0KHVybDogc3RyaW5nLCBzY3JpcHQ6IHN0cmluZywgb3B0aW9ucz86IHtcbiAgICB3YWl0Rm9yU2VsZWN0b3I/OiBzdHJpbmc7XG4gICAgdGltZW91dD86IG51bWJlcjtcbiAgICByZXR1cm5UeXBlPzogJ2pzb24nIHwgJ3RleHQnIHwgJ3NjcmVlbnNob3QnO1xuICB9KTogUHJvbWlzZTxhbnk+IHtcbiAgICBjb25zdCB3YWl0Rm9yU2VsZWN0b3IgPSBvcHRpb25zPy53YWl0Rm9yU2VsZWN0b3IgfHwgJyc7XG4gICAgY29uc3QgdGltZW91dCA9IG9wdGlvbnM/LnRpbWVvdXQgfHwgMzAwMDA7XG4gICAgY29uc3QgcmV0dXJuVHlwZSA9IG9wdGlvbnM/LnJldHVyblR5cGUgfHwgJ2pzb24nO1xuXG4gICAgY29uc3QgY29kZSA9IGBcbiAgICAgIGV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uICh7IHBhZ2UgfSkge1xuICAgICAgICBhd2FpdCBwYWdlLmdvdG8oXCIke3VybH1cIiwgeyB3YWl0VW50aWw6ICduZXR3b3JraWRsZTAnIH0pO1xuXG4gICAgICAgICR7d2FpdEZvclNlbGVjdG9yID8gYGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKFwiJHt3YWl0Rm9yU2VsZWN0b3J9XCIsIHsgdGltZW91dDogJHt0aW1lb3V0fSB9KTtgIDogJyd9XG5cbiAgICAgICAgLy8gRXhlY3V0ZSBjdXN0b20gc2NyaXB0XG4gICAgICAgIGNvbnN0IHNjcmlwdFJlc3VsdCA9IGF3YWl0IHBhZ2UuZXZhbHVhdGUoKCkgPT4ge1xuICAgICAgICAgICR7c2NyaXB0fVxuICAgICAgICB9KTtcblxuICAgICAgICBsZXQgZmluYWxSZXN1bHQgPSBzY3JpcHRSZXN1bHQ7XG5cbiAgICAgICAgaWYgKFwiJHtyZXR1cm5UeXBlfVwiID09PSAnc2NyZWVuc2hvdCcpIHtcbiAgICAgICAgICBjb25zdCBzY3JlZW5zaG90ID0gYXdhaXQgcGFnZS5zY3JlZW5zaG90KHtcbiAgICAgICAgICAgIGVuY29kaW5nOiAnYmFzZTY0JyxcbiAgICAgICAgICAgIHR5cGU6ICdwbmcnXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgZmluYWxSZXN1bHQgPSB7XG4gICAgICAgICAgICBzY3JpcHRSZXN1bHQ6IHNjcmlwdFJlc3VsdCxcbiAgICAgICAgICAgIHNjcmVlbnNob3Q6IHNjcmVlbnNob3RcbiAgICAgICAgICB9O1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICB1cmw6IFwiJHt1cmx9XCIsXG4gICAgICAgICAgICByZXN1bHQ6IGZpbmFsUmVzdWx0LFxuICAgICAgICAgICAgcmV0dXJuVHlwZTogXCIke3JldHVyblR5cGV9XCIsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH0sXG4gICAgICAgICAgdHlwZTogXCJhcHBsaWNhdGlvbi9qc29uXCJcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICBgO1xuXG4gICAgcmV0dXJuIHRoaXMuZXhlY3V0ZUZ1bmN0aW9uKGNvZGUpO1xuICB9XG5cbiAgLyoqXG4gICAqIFNtYXJ0IGNvbnRlbnQgZXh0cmFjdGlvbiB3aXRoIG11bHRpcGxlIHN0cmF0ZWdpZXNcbiAgICovXG4gIGFzeW5jIHNtYXJ0RXh0cmFjdCh1cmw6IHN0cmluZywgZXh0cmFjdGlvbkdvYWxzOiBzdHJpbmdbXSk6IFByb21pc2U8YW55PiB7XG4gICAgY29uc3QgY29kZSA9IGBcbiAgICAgIGV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uICh7IHBhZ2UgfSkge1xuICAgICAgICBhd2FpdCBwYWdlLmdvdG8oXCIke3VybH1cIiwgeyB3YWl0VW50aWw6ICduZXR3b3JraWRsZTAnIH0pO1xuXG4gICAgICAgIGNvbnN0IGdvYWxzID0gJHtKU09OLnN0cmluZ2lmeShleHRyYWN0aW9uR29hbHMpfTtcbiAgICAgICAgY29uc3QgcmVzdWx0cyA9IHt9O1xuXG4gICAgICAgIC8vIENvbW1vbiBleHRyYWN0aW9uIHBhdHRlcm5zXG4gICAgICAgIGNvbnN0IGV4dHJhY3RvcnMgPSB7XG4gICAgICAgICAgcHJpY2VzOiAoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBwcmljZVNlbGVjdG9ycyA9IFtcbiAgICAgICAgICAgICAgJ1tjbGFzcyo9XCJwcmljZVwiXScsICdbaWQqPVwicHJpY2VcIl0nLCAnLmNvc3QnLCAnLmFtb3VudCcsXG4gICAgICAgICAgICAgICdbZGF0YS10ZXN0aWQqPVwicHJpY2VcIl0nLCAnLmN1cnJlbmN5JywgJ1tjbGFzcyo9XCJkb2xsYXJcIl0nXG4gICAgICAgICAgICBdO1xuICAgICAgICAgICAgY29uc3QgcHJpY2VzID0gW107XG4gICAgICAgICAgICBwcmljZVNlbGVjdG9ycy5mb3JFYWNoKHNlbGVjdG9yID0+IHtcbiAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbChzZWxlY3RvcikuZm9yRWFjaChlbCA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgdGV4dCA9IGVsLnRleHRDb250ZW50Py50cmltKCk7XG4gICAgICAgICAgICAgICAgaWYgKHRleHQgJiYgL1skwqPigqzCpeKCuV18XFxcXGQrXFxcXC5cXFxcZHsyfS8udGVzdCh0ZXh0KSkge1xuICAgICAgICAgICAgICAgICAgcHJpY2VzLnB1c2goe1xuICAgICAgICAgICAgICAgICAgICB0ZXh0OiB0ZXh0LFxuICAgICAgICAgICAgICAgICAgICBzZWxlY3Rvcjogc2VsZWN0b3IsXG4gICAgICAgICAgICAgICAgICAgIGVsZW1lbnQ6IGVsLnRhZ05hbWVcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBwcmljZXM7XG4gICAgICAgICAgfSxcblxuICAgICAgICAgIGNvbnRhY3Q6ICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbnRhY3RTZWxlY3RvcnMgPSBbXG4gICAgICAgICAgICAgICdbaHJlZl49XCJtYWlsdG86XCJdJywgJ1tocmVmXj1cInRlbDpcIl0nLCAnLmNvbnRhY3QnLCAnLmVtYWlsJywgJy5waG9uZSdcbiAgICAgICAgICAgIF07XG4gICAgICAgICAgICBjb25zdCBjb250YWN0cyA9IFtdO1xuICAgICAgICAgICAgY29udGFjdFNlbGVjdG9ycy5mb3JFYWNoKHNlbGVjdG9yID0+IHtcbiAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbChzZWxlY3RvcikuZm9yRWFjaChlbCA9PiB7XG4gICAgICAgICAgICAgICAgY29udGFjdHMucHVzaCh7XG4gICAgICAgICAgICAgICAgICB0ZXh0OiBlbC50ZXh0Q29udGVudD8udHJpbSgpLFxuICAgICAgICAgICAgICAgICAgaHJlZjogZWwuZ2V0QXR0cmlidXRlKCdocmVmJyksXG4gICAgICAgICAgICAgICAgICB0eXBlOiBlbC5nZXRBdHRyaWJ1dGUoJ2hyZWYnKT8uc3RhcnRzV2l0aCgnbWFpbHRvOicpID8gJ2VtYWlsJyA6ICdwaG9uZSdcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBjb250YWN0cztcbiAgICAgICAgICB9LFxuXG4gICAgICAgICAgcHJvZHVjdHM6ICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHByb2R1Y3RTZWxlY3RvcnMgPSBbXG4gICAgICAgICAgICAgICcucHJvZHVjdCcsICdbY2xhc3MqPVwicHJvZHVjdFwiXScsICcuaXRlbScsICdbZGF0YS10ZXN0aWQqPVwicHJvZHVjdFwiXSdcbiAgICAgICAgICAgIF07XG4gICAgICAgICAgICBjb25zdCBwcm9kdWN0cyA9IFtdO1xuICAgICAgICAgICAgcHJvZHVjdFNlbGVjdG9ycy5mb3JFYWNoKHNlbGVjdG9yID0+IHtcbiAgICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbChzZWxlY3RvcikuZm9yRWFjaChlbCA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgdGl0bGUgPSBlbC5xdWVyeVNlbGVjdG9yKCdoMSwgaDIsIGgzLCAudGl0bGUsIFtjbGFzcyo9XCJ0aXRsZVwiXScpPy50ZXh0Q29udGVudD8udHJpbSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHByaWNlID0gZWwucXVlcnlTZWxlY3RvcignW2NsYXNzKj1cInByaWNlXCJdLCAuY29zdCcpPy50ZXh0Q29udGVudD8udHJpbSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGltYWdlID0gZWwucXVlcnlTZWxlY3RvcignaW1nJyk/LnNyYztcbiAgICAgICAgICAgICAgICBpZiAodGl0bGUpIHtcbiAgICAgICAgICAgICAgICAgIHByb2R1Y3RzLnB1c2goeyB0aXRsZSwgcHJpY2UsIGltYWdlIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBwcm9kdWN0cztcbiAgICAgICAgICB9LFxuXG4gICAgICAgICAgdGV4dDogKCkgPT4ge1xuICAgICAgICAgICAgLy8gRXh0cmFjdCBtYWluIGNvbnRlbnRcbiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRTZWxlY3RvcnMgPSBbJ21haW4nLCAnYXJ0aWNsZScsICcuY29udGVudCcsICcjY29udGVudCcsICcucG9zdCddO1xuICAgICAgICAgICAgbGV0IGNvbnRlbnQgPSAnJztcbiAgICAgICAgICAgIGZvciAoY29uc3Qgc2VsZWN0b3Igb2YgY29udGVudFNlbGVjdG9ycykge1xuICAgICAgICAgICAgICBjb25zdCBlbCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3Ioc2VsZWN0b3IpO1xuICAgICAgICAgICAgICBpZiAoZWwpIHtcbiAgICAgICAgICAgICAgICBjb250ZW50ID0gZWwudGV4dENvbnRlbnQ/LnRyaW0oKSB8fCAnJztcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjb250ZW50KSB7XG4gICAgICAgICAgICAgIGNvbnRlbnQgPSBkb2N1bWVudC5ib2R5LnRleHRDb250ZW50Py50cmltKCkgfHwgJyc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gY29udGVudC5zdWJzdHJpbmcoMCwgNTAwMCk7IC8vIExpbWl0IHRvIDUwMDAgY2hhcnNcbiAgICAgICAgICB9LFxuXG4gICAgICAgICAgbGlua3M6ICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGxpbmtzID0gW107XG4gICAgICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCdhW2hyZWZdJykuZm9yRWFjaChlbCA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGhyZWYgPSBlbC5nZXRBdHRyaWJ1dGUoJ2hyZWYnKTtcbiAgICAgICAgICAgICAgY29uc3QgdGV4dCA9IGVsLnRleHRDb250ZW50Py50cmltKCk7XG4gICAgICAgICAgICAgIGlmIChocmVmICYmIHRleHQgJiYgIWhyZWYuc3RhcnRzV2l0aCgnIycpKSB7XG4gICAgICAgICAgICAgICAgbGlua3MucHVzaCh7XG4gICAgICAgICAgICAgICAgICB1cmw6IG5ldyBVUkwoaHJlZiwgd2luZG93LmxvY2F0aW9uLmhyZWYpLmhyZWYsXG4gICAgICAgICAgICAgICAgICB0ZXh0OiB0ZXh0XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIGxpbmtzLnNsaWNlKDAsIDUwKTsgLy8gTGltaXQgdG8gNTAgbGlua3NcbiAgICAgICAgICB9XG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gRXhlY3V0ZSBleHRyYWN0b3JzIGJhc2VkIG9uIGdvYWxzXG4gICAgICAgIGdvYWxzLmZvckVhY2goZ29hbCA9PiB7XG4gICAgICAgICAgY29uc3QgZ29hbExvd2VyID0gZ29hbC50b0xvd2VyQ2FzZSgpO1xuICAgICAgICAgIGlmIChnb2FsTG93ZXIuaW5jbHVkZXMoJ3ByaWNlJykgfHwgZ29hbExvd2VyLmluY2x1ZGVzKCdjb3N0JykpIHtcbiAgICAgICAgICAgIHJlc3VsdHMucHJpY2VzID0gZXh0cmFjdG9ycy5wcmljZXMoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKGdvYWxMb3dlci5pbmNsdWRlcygnY29udGFjdCcpIHx8IGdvYWxMb3dlci5pbmNsdWRlcygnZW1haWwnKSB8fCBnb2FsTG93ZXIuaW5jbHVkZXMoJ3Bob25lJykpIHtcbiAgICAgICAgICAgIHJlc3VsdHMuY29udGFjdCA9IGV4dHJhY3RvcnMuY29udGFjdCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAoZ29hbExvd2VyLmluY2x1ZGVzKCdwcm9kdWN0JykgfHwgZ29hbExvd2VyLmluY2x1ZGVzKCdpdGVtJykpIHtcbiAgICAgICAgICAgIHJlc3VsdHMucHJvZHVjdHMgPSBleHRyYWN0b3JzLnByb2R1Y3RzKCk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChnb2FsTG93ZXIuaW5jbHVkZXMoJ3RleHQnKSB8fCBnb2FsTG93ZXIuaW5jbHVkZXMoJ2NvbnRlbnQnKSkge1xuICAgICAgICAgICAgcmVzdWx0cy50ZXh0ID0gZXh0cmFjdG9ycy50ZXh0KCk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChnb2FsTG93ZXIuaW5jbHVkZXMoJ2xpbmsnKSB8fCBnb2FsTG93ZXIuaW5jbHVkZXMoJ3VybCcpKSB7XG4gICAgICAgICAgICByZXN1bHRzLmxpbmtzID0gZXh0cmFjdG9ycy5saW5rcygpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gSWYgbm8gc3BlY2lmaWMgZ29hbHMsIGV4dHJhY3QgZXZlcnl0aGluZ1xuICAgICAgICBpZiAoZ29hbHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgT2JqZWN0LmtleXMoZXh0cmFjdG9ycykuZm9yRWFjaChrZXkgPT4ge1xuICAgICAgICAgICAgcmVzdWx0c1trZXldID0gZXh0cmFjdG9yc1trZXldKCk7XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIHVybDogXCIke3VybH1cIixcbiAgICAgICAgICAgIGV4dHJhY3Rpb25Hb2FsczogZ29hbHMsXG4gICAgICAgICAgICByZXN1bHRzOiByZXN1bHRzLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICB9LFxuICAgICAgICAgIHR5cGU6IFwiYXBwbGljYXRpb24vanNvblwiXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgYDtcblxuICAgIHJldHVybiB0aGlzLmV4ZWN1dGVGdW5jdGlvbihjb2RlKTtcbiAgfVxuXG4gIC8vIEdldCBzZXJ2aWNlIHN0YXRpc3RpY3NcbiAgZ2V0U3RhdHMoKTogYW55IHtcbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxLZXlzOiB0aGlzLmFwaUtleXMubGVuZ3RoLFxuICAgICAgaGVhbHRoeUtleXM6IHRoaXMuZ2V0SGVhbHRoeUtleXMoKS5sZW5ndGgsXG4gICAgICBrZXlVc2FnZTogT2JqZWN0LmZyb21FbnRyaWVzKHRoaXMua2V5VXNhZ2VDb3VudCksXG4gICAgICBrZXlFcnJvcnM6IE9iamVjdC5mcm9tRW50cmllcyh0aGlzLmtleUVycm9ycylcbiAgICB9O1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IEJyb3dzZXJsZXNzU2VydmljZTtcbiJdLCJuYW1lcyI6WyJCcm93c2VybGVzc1NlcnZpY2UiLCJjb25zdHJ1Y3RvciIsImFwaUtleXMiLCJjdXJyZW50S2V5SW5kZXgiLCJrZXlVc2FnZUNvdW50IiwiTWFwIiwia2V5RXJyb3JzIiwiTUFYX1JFVFJJRVMiLCJFUlJPUl9USFJFU0hPTEQiLCJFTkRQT0lOVCIsImluaXRpYWxpemVLZXlzIiwiZ2V0SW5zdGFuY2UiLCJpbnN0YW5jZSIsImtleXNTdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiQlJPV1NFUkxFU1NfQVBJX0tFWVMiLCJjb25zb2xlIiwiZXJyb3IiLCJzcGxpdCIsIm1hcCIsImtleSIsInRyaW0iLCJmaWx0ZXIiLCJCb29sZWFuIiwibG9nIiwibGVuZ3RoIiwiZm9yRWFjaCIsInNldCIsImdldE5leHRBcGlLZXkiLCJFcnJvciIsImJlc3RLZXkiLCJiZXN0U2NvcmUiLCJjYWxjdWxhdGVLZXlTY29yZSIsInNjb3JlIiwidXNhZ2UiLCJnZXQiLCJlcnJvcnMiLCJpbmNyZW1lbnRLZXlVc2FnZSIsImN1cnJlbnRVc2FnZSIsImluY3JlbWVudEtleUVycm9yIiwiY3VycmVudEVycm9ycyIsImlzS2V5SGVhbHRoeSIsImdldEhlYWx0aHlLZXlzIiwiZXhlY3V0ZUZ1bmN0aW9uIiwiY29kZSIsImNvbnRleHQiLCJjb25maWciLCJoZWFsdGh5S2V5cyIsImNsZWFyIiwibGFzdEVycm9yIiwiYXR0ZW1wdCIsImFwaUtleSIsInJlc3BvbnNlIiwibWFrZVJlcXVlc3QiLCJpc1JhdGVMaW1pdEVycm9yIiwiY3VycmVudEtleSIsInVybCIsInJlcXVlc3RCb2R5IiwiaGVhZGVycyIsInVzZXJBZ2VudCIsImZldGNoIiwibWV0aG9kIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzaWduYWwiLCJBYm9ydFNpZ25hbCIsInRpbWVvdXQiLCJvayIsImVycm9yVGV4dCIsInRleHQiLCJzdGF0dXMiLCJyZXN1bHQiLCJqc29uIiwibWVzc2FnZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJuYXZpZ2F0ZUFuZEV4dHJhY3QiLCJzZWxlY3RvciIsInNlYXJjaEFuZEV4dHJhY3QiLCJxdWVyeSIsInNlYXJjaEVuZ2luZSIsInNlYXJjaFVybCIsImVuY29kZVVSSUNvbXBvbmVudCIsInRha2VTY3JlZW5zaG90Iiwib3B0aW9ucyIsImZ1bGxQYWdlIiwicXVhbGl0eSIsImZpbGxGb3JtIiwiZm9ybURhdGEiLCJzdWJtaXRBZnRlckZpbGwiLCJ3YWl0Rm9yTmF2aWdhdGlvbiIsImZvcm1TZWxlY3RvciIsInNvbHZlQ2FwdGNoYSIsImNhcHRjaGFUeXBlIiwiZXhlY3V0ZUFkdmFuY2VkU2NyaXB0Iiwic2NyaXB0Iiwid2FpdEZvclNlbGVjdG9yIiwicmV0dXJuVHlwZSIsInNtYXJ0RXh0cmFjdCIsImV4dHJhY3Rpb25Hb2FscyIsImdldFN0YXRzIiwidG90YWxLZXlzIiwia2V5VXNhZ2UiLCJPYmplY3QiLCJmcm9tRW50cmllcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            // Sort models by order for fallback\n            const sortedModels = [\n                ...browsingConfig.browsing_models\n            ].sort((a, b)=>a.order - b.order);\n            console.log(`[Browsing Execution] Starting browsing with ${sortedModels.length} models configured`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            let lastError = null;\n            // Try each model in order (strict fallback pattern)\n            for (const model of sortedModels){\n                try {\n                    console.log(`[Browsing Execution] Attempting with ${model.provider}/${model.model}`);\n                    // First, perform the web browsing\n                    const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                    if (!browsingResult.success) {\n                        throw new Error(browsingResult.error || 'Browsing failed');\n                    }\n                    // Then, use the AI model to process the browsing results\n                    const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                    if (aiResult.success) {\n                        console.log(`[Browsing Execution] ✅ Success with ${model.provider}/${model.model}`);\n                        return {\n                            success: true,\n                            content: aiResult.content,\n                            modelUsed: model.model,\n                            providerUsed: model.provider,\n                            browsingData: browsingResult.data\n                        };\n                    } else {\n                        throw new Error(aiResult.error || 'AI processing failed');\n                    }\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                    lastError = errorMessage;\n                    console.warn(`[Browsing Execution] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                    continue;\n                }\n            }\n            // All models failed\n            return {\n                success: false,\n                error: `All browsing models failed. Last error: ${lastError}`\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtract(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtract(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        return `You are an AI assistant that processes web browsing results to answer user queries.\n\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\nWEB BROWSING RESULTS:\n${dataStr}\n\nINSTRUCTIONS:\n1. Analyze the browsing results carefully\n2. Extract relevant information that answers the user's query\n3. Provide a comprehensive, well-structured response\n4. If the browsing results don't contain relevant information, say so clearly\n5. Include specific details, numbers, dates, and facts from the browsing results\n6. Organize the information in a clear, readable format\n7. Cite sources when possible (URLs, website names, etc.)\n\nPlease provide a helpful response based on the browsing results:`;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: model.model,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: model.model,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: model.model,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                content = result.choices?.[0]?.message?.content;\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ })

};
;