'use client';

import { useState, useEffect, FormEvent, useCallback, useMemo } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation'; // For accessing route params
import { type NewApiKey, type DisplayApiKey } from '@/types/apiKeys';
import { llmProviders } from '@/config/models'; // Ensure path is correct
import { PREDEFINED_ROLES, Role, getRoleById } from '@/config/roles';
import { ArrowLeftIcon, TrashIcon, Cog6ToothIcon, CheckCircleIcon, ShieldCheckIcon, InformationCircleIcon, CloudArrowDownIcon, PlusCircleIcon, XCircleIcon, PlusIcon, KeyIcon, PencilIcon, GlobeAltIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { Tooltip } from 'react-tooltip';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';
import { useManageKeysPrefetch } from '@/hooks/useManageKeysPrefetch';
import ManageKeysLoadingSkeleton, { CompactManageKeysLoadingSkeleton } from '@/components/ManageKeysLoadingSkeleton';
import { useRoutingSetupPrefetch } from '@/hooks/useRoutingSetupPrefetch';
import { useNavigationSafe } from '@/contexts/NavigationContext';
import { ApiKeyManager } from '@/components/UserApiKeys/ApiKeyManager';
import { TierGuard } from '@/components/TierEnforcement';

// Interface for the richer ModelInfo from /api/providers/list-models
interface FetchedModelInfo {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  version?: string;
  family?: string;
  input_token_limit?: number;
  output_token_limit?: number;
  context_window?: number;
  modality?: string;
  provider_id?: string;
  provider_specific?: Record<string, any>;
}

// Type for a single custom API configuration (matching MyModelsPage)
interface BrowsingModel {
  id: string;
  provider: string;
  model: string;
  api_key: string;
  temperature?: number;
  order: number; // For fallback ordering
}

interface CustomApiConfig {
  id: string;
  name: string;
  created_at: string;
  browsing_enabled?: boolean;
  browsing_models?: BrowsingModel[];
}

interface ApiKeyWithRoles extends DisplayApiKey {
  assigned_roles: Role[];
  // Note: is_default_general_chat_model is already included from DisplayApiKey
}

// Updated: Interface for User-Defined Custom Roles (now global)
interface UserCustomRole {
  id: string; // UUID for the custom role entry itself (database ID)
  // custom_api_config_id: string; // Removed - roles are now global per user
  user_id: string; // Belongs to this user
  role_id: string; // The user-defined short ID (e.g., "my_summarizer"), unique per user
  name: string;    // User-friendly name
  description?: string | null;
  created_at: string;
  updated_at: string;
}

// Extended Role type for combined list in modal
interface DisplayableRole extends Role {
  isCustom?: boolean;
  databaseId?: string; // Actual DB ID (UUID) for custom roles, for delete/edit operations
}

// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label
const PROVIDER_OPTIONS = llmProviders.map(p => ({ value: p.id, label: p.name }));



export default function ConfigDetailsPage() {
  const params = useParams();
  const configId = params.configId as string;

  // Confirmation modal hook
  const confirmation = useConfirmation();

  // Navigation hook with safe context
  const navigationContext = useNavigationSafe();
  const navigateOptimistically = navigationContext?.navigateOptimistically || ((href: string) => {
    window.location.href = href;
  });

  // Prefetch hooks
  const { getCachedData, isCached, clearCache } = useManageKeysPrefetch();
  const { createHoverPrefetch: createRoutingHoverPrefetch } = useRoutingSetupPrefetch();

  const [configDetails, setConfigDetails] = useState<CustomApiConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState<boolean>(true);
  const [showOptimisticLoading, setShowOptimisticLoading] = useState<boolean>(false);

  // Provider state now stores the slug (p.id)
  const [provider, setProvider] = useState<string>(PROVIDER_OPTIONS[0]?.value || 'openai'); // Stores slug
  const [predefinedModelId, setPredefinedModelId] = useState<string>('');
  const [apiKeyRaw, setApiKeyRaw] = useState<string>('');
  const [label, setLabel] = useState<string>('');
  const [temperature, setTemperature] = useState<number>(1.0);
  const [isSavingKey, setIsSavingKey] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for dynamic model fetching
  const [fetchedProviderModels, setFetchedProviderModels] = useState<FetchedModelInfo[] | null>(null);
  const [isFetchingProviderModels, setIsFetchingProviderModels] = useState<boolean>(false);
  const [fetchProviderModelsError, setFetchProviderModelsError] = useState<string | null>(null);

  const [savedKeysWithRoles, setSavedKeysWithRoles] = useState<ApiKeyWithRoles[]>([]);
  const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = useState<boolean>(true);
  const [isDeletingKey, setIsDeletingKey] = useState<string | null>(null);
  const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = useState<string | null>(null);

  const [editingRolesApiKey, setEditingRolesApiKey] = useState<ApiKeyWithRoles | null>(null);

  // State for editing API keys
  const [editingApiKey, setEditingApiKey] = useState<ApiKeyWithRoles | null>(null);
  const [editTemperature, setEditTemperature] = useState<number>(1.0);
  const [editPredefinedModelId, setEditPredefinedModelId] = useState<string>('');
  const [isSavingEdit, setIsSavingEdit] = useState<boolean>(false);

  // State for User-Defined Custom Roles
  const [userCustomRoles, setUserCustomRoles] = useState<UserCustomRole[]>([]);
  const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = useState<boolean>(false);
  const [userCustomRolesError, setUserCustomRolesError] = useState<string | null>(null);
  const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = useState<boolean>(false);
  const [newCustomRoleId, setNewCustomRoleId] = useState<string>('');
  const [newCustomRoleName, setNewCustomRoleName] = useState<string>('');
  const [newCustomRoleDescription, setNewCustomRoleDescription] = useState<string>('');
  const [isSavingCustomRole, setIsSavingCustomRole] = useState<boolean>(false);
  const [createCustomRoleError, setCreateCustomRoleError] = useState<string | null>(null);
  const [deletingCustomRoleId, setDeletingCustomRoleId] = useState<string | null>(null); // stores the DB ID (UUID) of the custom role

  // State for tab management
  const [activeTab, setActiveTab] = useState<'provider-keys' | 'user-api-keys' | 'browsing-config'>('provider-keys');

  // Browsing configuration state
  const [browsingEnabled, setBrowsingEnabled] = useState<boolean>(false);
  const [browsingModels, setBrowsingModels] = useState<BrowsingModel[]>([]);
  const [isSavingBrowsing, setIsSavingBrowsing] = useState<boolean>(false);

  // State for fetched models (reuse existing fetchedProviderModels)
  // This will be populated by the existing fetchModelsFromDatabase function



  // Fetch config details with optimistic loading
  const fetchConfigDetails = useCallback(async () => {
    if (!configId) return;

    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.configDetails) {
      console.log(`⚡ [MANAGE KEYS] Using cached config data for: ${configId}`);
      setConfigDetails(cachedData.configDetails);
      // Load browsing settings from cached config
      if (cachedData.configDetails.browsing_enabled !== undefined) {
        setBrowsingEnabled(cachedData.configDetails.browsing_enabled);
      }
      if (cachedData.configDetails.browsing_models) {
        setBrowsingModels(cachedData.configDetails.browsing_models);
      }
      setIsLoadingConfig(false);
      return;
    }

    // Show optimistic loading for first-time visits
    if (!isCached(configId)) {
      setShowOptimisticLoading(true);
    }

    setIsLoadingConfig(true);
    setError(null);

    try {
      const res = await fetch(`/api/custom-configs`);
      if (!res.ok) {
        const errData = await res.json();
        throw new Error(errData.error || 'Failed to fetch configurations list');
      }
      const allConfigs: CustomApiConfig[] = await res.json();
      const currentConfig = allConfigs.find(c => c.id === configId);
      if (!currentConfig) throw new Error('Configuration not found in the list.');
      setConfigDetails(currentConfig);
      // Load browsing settings from fetched config
      if (currentConfig.browsing_enabled !== undefined) {
        setBrowsingEnabled(currentConfig.browsing_enabled);
      }
      if (currentConfig.browsing_models) {
        setBrowsingModels(currentConfig.browsing_models);
      }
    } catch (err: any) {
      setError(`Error loading model configuration: ${err.message}`);
      setConfigDetails(null);
    } finally {
      setIsLoadingConfig(false);
      setShowOptimisticLoading(false);
    }
  }, [configId, getCachedData, isCached]);

  useEffect(() => {
    fetchConfigDetails();
  }, [fetchConfigDetails]);

  // New: Function to fetch all models from the database with caching
  const fetchModelsFromDatabase = useCallback(async () => {
    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.models) {
      console.log(`⚡ [MANAGE KEYS] Using cached models data for: ${configId}`);
      setFetchedProviderModels(cachedData.models);
      setIsFetchingProviderModels(false);
      return;
    }

    setIsFetchingProviderModels(true);
    setFetchProviderModelsError(null);
    setFetchedProviderModels(null);
    try {
      // The new API doesn't need a specific provider or API key in the body to list models
      const response = await fetch('/api/providers/list-models', {
        method: 'POST', // Still a POST as per the route's definition
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}), // Send empty or dummy body
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch models from database.');
      }
      if (data.models) {
        setFetchedProviderModels(data.models);
      } else {
        setFetchedProviderModels([]);
      }
    } catch (err: any) {
      setFetchProviderModelsError(`Error fetching models: ${err.message}`);
      setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI
    } finally {
      setIsFetchingProviderModels(false);
    }
  }, [configId, getCachedData]);

  // New: Fetch all models from DB when configId is available (i.e., page is ready)
  useEffect(() => {
    if (configId) { // Ensures we are on the correct page context
      fetchModelsFromDatabase();
    }
  }, [configId, fetchModelsFromDatabase]);

  // Updated: Function to fetch all global custom roles for the authenticated user with caching
  const fetchUserCustomRoles = useCallback(async () => {
    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.userCustomRoles) {
      console.log(`⚡ [MANAGE KEYS] Using cached custom roles data for: ${configId}`);
      setUserCustomRoles(cachedData.userCustomRoles);
      setIsLoadingUserCustomRoles(false);
      return;
    }

    setIsLoadingUserCustomRoles(true);
    setUserCustomRolesError(null);
    try {
      const response = await fetch(`/api/user/custom-roles`); // New global endpoint
      
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json(); // Attempt to parse error as JSON
        } catch (e) {
          // If error response is not JSON, use text or a generic error
          const errorText = await response.text().catch(() => `HTTP error ${response.status}`);
          errorData = { error: errorText };
        }

        const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : `Failed to fetch custom roles (status: ${response.status})`);
        
        if (response.status === 401) {
            setUserCustomRolesError(errorMessage);
        } else {
            throw new Error(errorMessage); // Throw for other errors to be caught by the main catch
        }
        setUserCustomRoles([]); // Clear roles if there was an error handled here
      } else {
        // Only call .json() here if response.ok and body hasn't been read
        const data: UserCustomRole[] = await response.json();
        setUserCustomRoles(data);
        // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try
      }
    } catch (err: any) {
      // This catch handles network errors from fetch() or errors thrown from !response.ok block
      setUserCustomRolesError(err.message);
      setUserCustomRoles([]); // Clear roles on error
    } finally {
      setIsLoadingUserCustomRoles(false);
    }
  }, []);

  // Fetch API keys and their roles for this config with optimistic loading
  const fetchKeysAndRolesForConfig = useCallback(async () => {
    if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available

    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {
      console.log(`⚡ [MANAGE KEYS] Using cached keys data for: ${configId}`);

      // Process cached keys with roles (same logic as below)
      const keysWithRolesPromises = cachedData.apiKeys.map(async (key: any) => {
        const rolesResponse = await fetch(`/api/keys/${key.id}/roles`);
        let assigned_roles: Role[] = [];
        if (rolesResponse.ok) {
          const roleAssignments: Array<{ role_name: string, role_details?: Role }> = await rolesResponse.json();

          assigned_roles = roleAssignments.map(ra => {
            const predefinedRole = getRoleById(ra.role_name);
            if (predefinedRole) return predefinedRole;

            const customRole = userCustomRoles.find(cr => cr.role_id === ra.role_name);
            if (customRole) {
              return {
                id: customRole.role_id,
                name: customRole.name,
                description: customRole.description || undefined
              };
            }
            return null;
          }).filter(Boolean) as Role[];
        }
        return {
          ...key,
          assigned_roles,
          is_default_general_chat_model: cachedData.defaultChatKeyId === key.id,
        };
      });

      const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);
      setSavedKeysWithRoles(resolvedKeysWithRoles);
      setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);
      setIsLoadingKeysAndRoles(false);
      return;
    }

    setIsLoadingKeysAndRoles(true);
    // Preserve config loading errors, clear others
    setError(prev => prev && prev.startsWith('Error loading model configuration:') ? prev : null);
    setSuccessMessage(null);
    try {
      // Fetch all keys for the config
      const keysResponse = await fetch(`/api/keys?custom_config_id=${configId}`);
      if (!keysResponse.ok) { const errorData = await keysResponse.json(); throw new Error(errorData.error || 'Failed to fetch API keys'); }
      const keys: DisplayApiKey[] = await keysResponse.json();

      // Fetch default general chat key
      const defaultKeyResponse = await fetch(`/api/custom-configs/${configId}/default-chat-key`);
      if (!defaultKeyResponse.ok) { /* Do not throw, default might not be set */ console.warn('Failed to fetch default chat key info'); }
      const defaultKeyData: DisplayApiKey | null = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;
      setDefaultGeneralChatKeyId(defaultKeyData?.id || null);

      // For each key, fetch its assigned roles
      const keysWithRolesPromises = keys.map(async (key) => {
        const rolesResponse = await fetch(`/api/keys/${key.id}/roles`);
        let assigned_roles: Role[] = [];
        if (rolesResponse.ok) {
          const roleAssignments: Array<{ role_name: string, role_details?: Role }> = await rolesResponse.json();
          
          assigned_roles = roleAssignments.map(ra => {
            // 1. Check predefined roles
            const predefinedRole = getRoleById(ra.role_name);
            if (predefinedRole) return predefinedRole;
            
            // 2. Check current user's global custom roles
            const customRole = userCustomRoles.find(cr => cr.role_id === ra.role_name);
            if (customRole) {
              return { 
                id: customRole.role_id, 
                name: customRole.name, 
                description: customRole.description || undefined 
              };
            }
            // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out
            return null;
          }).filter(Boolean) as Role[]; // filter(Boolean) removes null entries
        }
        return {
          ...key,
          assigned_roles,
          is_default_general_chat_model: defaultKeyData?.id === key.id,
        };
      });
      const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);
      setSavedKeysWithRoles(resolvedKeysWithRoles);
    } catch (err: any) {
      setError(prev => prev ? `${prev}; ${err.message}` : err.message); // Append if there was a config load error
    } finally {
      setIsLoadingKeysAndRoles(false);
    }
  }, [configId, userCustomRoles]); // Added userCustomRoles to dependency array



  useEffect(() => {
    if (configDetails) {
      fetchUserCustomRoles(); // Call to fetch custom roles
    }
  }, [configDetails, fetchUserCustomRoles]); // Only depends on configDetails and the stable fetchUserCustomRoles

  // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready
  useEffect(() => {
    // Ensure userCustomRoles is not in its initial undefined/null state from useState([])
    // and actually contains data (or an empty array confirming fetch completion)
    if (configDetails && userCustomRoles) { 
      fetchKeysAndRolesForConfig();
    }
    // This effect runs if configDetails changes, userCustomRoles (state) changes,
    // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).
    // This is the desired behavior: re-fetch keys/roles if custom roles change.
  }, [configDetails, userCustomRoles, fetchKeysAndRolesForConfig]);

  // Updated: Memoize model options based on selected provider and fetched models
  const modelOptions = useMemo(() => {
    if (fetchedProviderModels) {
      const currentProviderDetails = llmProviders.find(p => p.id === provider);
      if (!currentProviderDetails) return [];

      // If the selected provider is "OpenRouter", show all fetched models
      // as an OpenRouter key can access any of them.
      if (currentProviderDetails.id === "openrouter") { // Matched against id for consistency
        return fetchedProviderModels
          .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
          .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // Custom logic for DeepSeek
      if (currentProviderDetails.id === "deepseek") {
        console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));
        const deepseekOptions: { value: string; label: string; provider_id?: string; }[] = [];
        const deepseekChatModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
        );
        console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));
        if (deepseekChatModel) {
          deepseekOptions.push({
            value: "deepseek-chat",
            label: "Deepseek V3", // User-friendly name
            provider_id: "deepseek",
          });
        }
        const deepseekReasonerModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
        );
        console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));
        if (deepseekReasonerModel) {
          deepseekOptions.push({
            value: "deepseek-reasoner",
            label: "DeepSeek R1-0528", // User-friendly name
            provider_id: "deepseek",
          });
        }
        // If for some reason the specific models are not found in fetchedProviderModels,
        // it's better to return an empty array or a message than all DeepSeek models unfiltered.
        // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.
        // For now, strictly showing only these two if found.
        return deepseekOptions.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // For other providers, filter by their specific provider_id
      return fetchedProviderModels
        .filter(model => model.provider_id === currentProviderDetails.id)
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
        .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }
    return []; // Return empty array if models haven't been fetched or if fetch failed.
  }, [fetchedProviderModels, provider]);

  // Model options for edit modal - filtered by the current key's provider
  const editModelOptions = useMemo(() => {
    if (fetchedProviderModels && editingApiKey) {
      const currentProviderDetails = llmProviders.find(p => p.id === editingApiKey.provider);
      if (!currentProviderDetails) return [];

      // If the provider is "OpenRouter", show all fetched models
      if (currentProviderDetails.id === "openrouter") {
        return fetchedProviderModels
          .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
          .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // Custom logic for DeepSeek
      if (currentProviderDetails.id === "deepseek") {
        const deepseekOptions: { value: string; label: string; provider_id?: string; }[] = [];
        const deepseekChatModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
        );
        if (deepseekChatModel) {
          deepseekOptions.push({
            value: "deepseek-chat",
            label: "Deepseek V3",
            provider_id: "deepseek",
          });
        }
        const deepseekReasonerModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
        );
        if (deepseekReasonerModel) {
          deepseekOptions.push({
            value: "deepseek-reasoner",
            label: "DeepSeek R1-0528",
            provider_id: "deepseek",
          });
        }
        return deepseekOptions.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // For other providers, filter by their specific provider_id
      return fetchedProviderModels
        .filter(model => model.provider_id === currentProviderDetails.id)
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
        .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }
    return [];
  }, [fetchedProviderModels, editingApiKey]);

  // Function to get model options for a specific provider (for browsing models)
  const getBrowsingModelOptions = useCallback((providerId: string) => {
    if (!fetchedProviderModels) return [];

    const currentProviderDetails = llmProviders.find(p => p.id === providerId);
    if (!currentProviderDetails) return [];

    // If the selected provider is "OpenRouter", show all fetched models
    if (currentProviderDetails.id === "openrouter") {
      return fetchedProviderModels
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
        .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }

    // Special handling for DeepSeek to show only specific models
    if (currentProviderDetails.id === "deepseek") {
      const targetModels = ["deepseek-chat", "deepseek-reasoner"];
      const deepseekOptions = fetchedProviderModels
        .filter(model =>
          model.provider_id === "deepseek" &&
          targetModels.some(target => model.id.includes(target))
        )
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }));

      return deepseekOptions.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }

    // For other providers, filter by their specific provider_id
    return fetchedProviderModels
      .filter(model => model.provider_id === currentProviderDetails.id)
      .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
      .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
  }, [fetchedProviderModels]);

  useEffect(() => {
    // Auto-select the first model from the dynamic modelOptions when provider changes or models load
    if (modelOptions.length > 0) {
      setPredefinedModelId(modelOptions[0].value);
    } else {
      setPredefinedModelId(''); // Clear if no models for provider
    }
  }, [modelOptions, provider]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider

  // Fetch models based on the provider's slug (p.id)
  useEffect(() => {
    if (provider) { // provider is now the slug
      // Logic to fetch models for the selected provider slug might need adjustment
      // if it was previously relying on the provider display name.
      // Assuming fetchProviderModels is adapted or already uses slugs.
      fetchModelsFromDatabase(); 
    }
  }, [provider, fetchModelsFromDatabase]);

  const handleSaveKey = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!configId) { setError('Configuration ID is missing.'); return; }

    // Frontend validation: Check for duplicate models
    const isDuplicateModel = savedKeysWithRoles.some(key => key.predefined_model_id === predefinedModelId);
    if (isDuplicateModel) {
      setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');
      return;
    }

    setIsSavingKey(true); setError(null); setSuccessMessage(null);

    // provider state variable already holds the slug
    const newKeyData: NewApiKey = {
      custom_api_config_id: configId,
      provider,
      predefined_model_id: predefinedModelId,
      api_key_raw: apiKeyRaw,
      label,
      temperature
    };

    // Store previous state for rollback on error
    const previousKeysState = [...savedKeysWithRoles];

    try {
      const response = await fetch('/api/keys', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(newKeyData) });
      const result = await response.json();
      if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');

      // Create optimistic key object with the returned data
      const newKey: ApiKeyWithRoles = {
        id: result.id,
        custom_api_config_id: configId,
        provider,
        predefined_model_id: predefinedModelId,
        label,
        temperature,
        status: 'active',
        created_at: new Date().toISOString(),
        last_used_at: null,
        is_default_general_chat_model: false,
        assigned_roles: []
      };

      // Optimistically add the new key to the list
      setSavedKeysWithRoles(prevKeys => [...prevKeys, newKey]);

      // Clear cache to ensure fresh data on next fetch
      clearCache(configId);

      setSuccessMessage(`API key "${label}" saved successfully!`);
      setProvider(PROVIDER_OPTIONS[0]?.value || 'openai');
      setApiKeyRaw('');
      setLabel('');
      setTemperature(1.0);

      // Reset model selection to first available option
      if (modelOptions.length > 0) {
        setPredefinedModelId(modelOptions[0].value);
      }
    } catch (err: any) {
      // Revert UI on error
      setSavedKeysWithRoles(previousKeysState);
      setError(`Save Key Error: ${err.message}`);
    }
    finally { setIsSavingKey(false); }
  };

  const handleEditKey = (key: ApiKeyWithRoles) => {
    setEditingApiKey(key);
    setEditTemperature(key.temperature || 1.0);
    setEditPredefinedModelId(key.predefined_model_id);
  };

  const handleSaveEdit = async () => {
    if (!editingApiKey) return;

    // Frontend validation: Check for duplicate models (excluding the current key being edited)
    const isDuplicateModel = savedKeysWithRoles.some(key =>
      key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId
    );
    if (isDuplicateModel) {
      setError('This model is already configured in this setup. Each model can only be used once per configuration.');
      return;
    }

    setIsSavingEdit(true);
    setError(null);
    setSuccessMessage(null);

    // Store previous state for rollback on error
    const previousKeysState = [...savedKeysWithRoles];

    // Optimistic UI update
    setSavedKeysWithRoles(prevKeys =>
      prevKeys.map(key => {
        if (key.id === editingApiKey.id) {
          return {
            ...key,
            temperature: editTemperature,
            predefined_model_id: editPredefinedModelId
          };
        }
        return key;
      })
    );

    try {
      const response = await fetch(`/api/keys?id=${editingApiKey.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          temperature: editTemperature,
          predefined_model_id: editPredefinedModelId
        })
      });

      const result = await response.json();
      if (!response.ok) {
        // Revert UI on error
        setSavedKeysWithRoles(previousKeysState);
        throw new Error(result.details || result.error || 'Failed to update API key');
      }

      // Clear cache to ensure fresh data on next fetch
      clearCache(configId);

      setSuccessMessage(`API key "${editingApiKey.label}" updated successfully!`);
      setEditingApiKey(null);
    } catch (err: any) {
      setError(`Update Key Error: ${err.message}`);
    } finally {
      setIsSavingEdit(false);
    }
  };

  const handleDeleteKey = (keyId: string, keyLabel: string) => {
    confirmation.showConfirmation(
      {
        title: 'Delete API Key',
        message: `Are you sure you want to delete the API key "${keyLabel}"? This will permanently remove the key and unassign all its roles. This action cannot be undone.`,
        confirmText: 'Delete API Key',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        setIsDeletingKey(keyId);
        setError(null);
        setSuccessMessage(null);

        // Store previous state for rollback on error
        const previousKeysState = [...savedKeysWithRoles];
        const previousDefaultKeyId = defaultGeneralChatKeyId;
        const keyToDelete = savedKeysWithRoles.find(key => key.id === keyId);

        // Optimistic UI update - immediately remove the key from the list
        setSavedKeysWithRoles(prevKeys => prevKeys.filter(key => key.id !== keyId));

        // If the deleted key was the default, clear the default
        if (keyToDelete?.is_default_general_chat_model) {
          setDefaultGeneralChatKeyId(null);
        }

        try {
          const response = await fetch(`/api/keys/${keyId}`, { method: 'DELETE' });
          const result = await response.json();
          if (!response.ok) {
            // Revert UI on error
            setSavedKeysWithRoles(previousKeysState);
            setDefaultGeneralChatKeyId(previousDefaultKeyId);

            // Special handling for 404 errors (key already deleted)
            if (response.status === 404) {
              // Key was already deleted, so the optimistic update was correct
              // Don't revert the UI, just show a different message
              setSavedKeysWithRoles(prevKeys => prevKeys.filter(key => key.id !== keyId));
              setSuccessMessage(`API key "${keyLabel}" was already deleted.`);
              return; // Don't throw error
            }

            throw new Error(result.details || result.error || 'Failed to delete API key');
          }

          // Clear cache to ensure fresh data on next fetch
          clearCache(configId);

          setSuccessMessage(`API key "${keyLabel}" deleted successfully!`);
        } catch (err: any) {
          setError(`Delete Key Error: ${err.message}`);
          throw err; // Re-throw to keep modal open on error
        } finally {
          setIsDeletingKey(null);
        }
      }
    );
  };

  const handleSetDefaultChatKey = async (apiKeyIdToSet: string) => {
    if (!configId) return;
    setError(null); setSuccessMessage(null);
    const previousKeysState = [...savedKeysWithRoles]; // Keep a copy in case of error
    const previousDefaultKeyId = defaultGeneralChatKeyId;

    // Optimistic UI update
    setSavedKeysWithRoles(prevKeys =>
      prevKeys.map(key => ({
        ...key,
        is_default_general_chat_model: key.id === apiKeyIdToSet,
      }))
    );
    setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID

    try {
      const response = await fetch(`/api/custom-configs/${configId}/default-key-handler/${apiKeyIdToSet}`, { method: 'PUT' });
      const result = await response.json();
      if (!response.ok) {
        // Revert UI on error
        setSavedKeysWithRoles(previousKeysState.map(k => ({ ...k }))); // Ensure deep copy for re-render
        setDefaultGeneralChatKeyId(previousDefaultKeyId);
        throw new Error(result.details || result.error || 'Failed to set default chat key');
      }

      // Clear cache to ensure fresh data on next fetch
      clearCache(configId);

      setSuccessMessage(result.message || 'Default general chat key updated!');
    } catch (err: any) {
      setError(`Set Default Error: ${err.message}`);
    }
  };

  const handleRoleToggle = async (apiKey: ApiKeyWithRoles, roleId: string, isAssigned: boolean) => {
    setError(null); setSuccessMessage(null);
    const endpoint = `/api/keys/${apiKey.id}/roles`;
    
    // For optimistic update, find role details from combined list (predefined or user's global custom roles)
    const allAvailableRoles: DisplayableRole[] = [
      ...PREDEFINED_ROLES.map(r => ({ ...r, isCustom: false })),
      ...userCustomRoles.map(cr => ({
        id: cr.role_id, 
        name: cr.name,
        description: cr.description || undefined,
        isCustom: true,
        databaseId: cr.id 
      }))
    ];
    const roleDetails = allAvailableRoles.find(r => r.id === roleId) || { id: roleId, name: roleId, description: '' } as DisplayableRole;

    const previousKeysState = savedKeysWithRoles.map(k => ({ 
      ...k, 
      assigned_roles: [...k.assigned_roles.map(r => ({...r}))] 
    })); // Deep copy
    let previousEditingRolesApiKey: ApiKeyWithRoles | null = null;
    if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {
      previousEditingRolesApiKey = { ...editingRolesApiKey, assigned_roles: [...editingRolesApiKey.assigned_roles.map(r => ({...r}))] };
    }

    // Optimistic UI update
    setSavedKeysWithRoles(prevKeys =>
      prevKeys.map(key => {
        if (key.id === apiKey.id) {
          const updatedRoles = isAssigned
            ? key.assigned_roles.filter(r => r.id !== roleId)
            : [...key.assigned_roles, roleDetails];
          return { ...key, assigned_roles: updatedRoles };
        }
        return key;
      })
    );

    if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {
      setEditingRolesApiKey(prevEditingKey => {
        if (!prevEditingKey) return null;
        const updatedRoles = isAssigned
          ? prevEditingKey.assigned_roles.filter(r => r.id !== roleId)
          : [...prevEditingKey.assigned_roles, roleDetails];
        return { ...prevEditingKey, assigned_roles: updatedRoles };
      });
    }

    try {
      let response;
      if (isAssigned) { // Role is currently assigned, so we want to unassign (DELETE)
        response = await fetch(`${endpoint}/${roleId}`, { method: 'DELETE' });
      } else { // Role is not assigned, so we want to assign (POST)
        response = await fetch(endpoint, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ role_name: roleId }) });
      }
      const result = await response.json();
      if (!response.ok) {
        // Revert UI on error
        setSavedKeysWithRoles(previousKeysState);
        if (previousEditingRolesApiKey) {
          setEditingRolesApiKey(previousEditingRolesApiKey);
        } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {
           const originalKeyData = previousKeysState.find(k => k.id === apiKey.id);
           if (originalKeyData) setEditingRolesApiKey(originalKeyData);
        }
        // Use the error message from the backend if available (e.g., for 409 conflict)
        const errorMessage = response.status === 409 && result.error 
                           ? result.error 
                           : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');
        throw new Error(errorMessage);
      }
      setSuccessMessage(result.message || `Role '${roleDetails.name}' ${isAssigned ? 'unassigned' : 'assigned'} successfully.`);
    } catch (err: any) {
      // err.message now contains the potentially more user-friendly message from the backend or a fallback
      setError(`Role Update Error: ${err.message}`); 
    }
  };



  const handleCreateCustomRole = async () => {
    // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.
    // configId is also not needed for creating a global role.
    if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {
      setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');
      return;
    }
    // Check against PREDEFINED_ROLES and the user's existing global custom roles
    if (PREDEFINED_ROLES.some(pr => pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || 
        userCustomRoles.some(cr => cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {
        setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');
        return;
    }
    if (!newCustomRoleName.trim()) {
      setCreateCustomRoleError('Role Name is required.');
      return;
    }
    setCreateCustomRoleError(null);
    setIsSavingCustomRole(true);
    try {
      const response = await fetch(`/api/user/custom-roles`, { // New global endpoint
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role_id: newCustomRoleId.trim(),
          name: newCustomRoleName.trim(),
          description: newCustomRoleDescription.trim(),
        }),
      });
      
      if (!response.ok) {
        // Try to parse the error response as JSON, but fallback if it's not JSON
        let errorResult;
        try {
          errorResult = await response.json();
        } catch (parseError) {
          // If JSON parsing fails, use the response text or a generic status message
          const errorText = await response.text().catch(() => `HTTP status ${response.status}`);
          errorResult = { error: "Server error, could not parse response.", details: errorText };
        }

        let displayError = errorResult.error || 'Failed to create custom role.';
        if (errorResult.details) {
            displayError += ` (Details: ${errorResult.details})`;
        } else if (errorResult.issues) {
            // If Zod issues, format them for better readability
            const issuesString = Object.entries(errorResult.issues)
                .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
                .join('; ');
            displayError += ` (Issues: ${issuesString})`;
        }
        throw new Error(displayError);
      }

      // If response IS ok, then parse the successful JSON response
      const result = await response.json(); 

      setNewCustomRoleId('');
      setNewCustomRoleName('');
      setNewCustomRoleDescription('');
      // setShowCreateCustomRoleForm(false); // User might want to add multiple roles

      // Clear cache to ensure fresh data on next fetch
      clearCache(configId);

      // Add the new role optimistically to the local state
      const newRole = {
        id: result.id,
        role_id: result.role_id,
        name: result.name,
        description: result.description,
        user_id: result.user_id,
        created_at: result.created_at,
        updated_at: result.updated_at
      };
      setUserCustomRoles(prev => [...prev, newRole]);

      setSuccessMessage(`Custom role '${result.name}' created successfully! It is now available globally.`);
    } catch (err: any) {
      setCreateCustomRoleError(err.message);
    } finally {
      setIsSavingCustomRole(false);
    }
  };

  const handleDeleteCustomRole = (customRoleDatabaseId: string, customRoleName: string) => {
    // configId is not needed for deleting a global role
    if (!customRoleDatabaseId) return;

    confirmation.showConfirmation(
      {
        title: 'Delete Custom Role',
        message: `Are you sure you want to delete the custom role "${customRoleName}"? This will unassign it from all API keys where it's currently used. This action cannot be undone.`,
        confirmText: 'Delete Role',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        setDeletingCustomRoleId(customRoleDatabaseId);
        setUserCustomRolesError(null);
        setCreateCustomRoleError(null);
        setSuccessMessage(null);
        try {
          const response = await fetch(`/api/user/custom-roles/${customRoleDatabaseId}`, { // New global endpoint
            method: 'DELETE',
          });
          const result = await response.json(); // Try to parse JSON for all responses
          if (!response.ok) {
            throw new Error(result.error || 'Failed to delete custom role');
          }
          // Optimistically remove from the local state
          setUserCustomRoles(prev => prev.filter(role => role.id !== customRoleDatabaseId));

          // Clear cache to ensure fresh data on next fetch
          clearCache(configId);

          setSuccessMessage(result.message || `Global custom role "${customRoleName}" deleted successfully.`);

          // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.
          // This ensures the displayed assigned roles for keys on this page are up-to-date.
          if (configId) { // Only if we are on a specific config page
            fetchKeysAndRolesForConfig();
          }
        } catch (err: any) {
          setUserCustomRolesError(`Error deleting role: ${err.message}`);
          throw err; // Re-throw to keep modal open on error
        } finally {
          setDeletingCustomRoleId(null);
        }
      }
    );
  };



  const renderManageRolesModal = () => {
    if (!editingRolesApiKey) return null;

    const combinedRoles: DisplayableRole[] = [
      ...PREDEFINED_ROLES.map(r => ({ ...r, isCustom: false })),
      ...userCustomRoles.map(cr => ({
        id: cr.role_id, // Use user-defined role_id for matching against assigned_roles
        name: cr.name,
        description: cr.description || undefined,
        isCustom: true,
        databaseId: cr.id // The actual DB ID (UUID) for delete operations
      }))
    ].sort((a, b) => a.name.localeCompare(b.name));

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
        <div className="bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col">
          <div className="flex justify-between items-center p-6 border-b border-gray-800/50">
            <h2 className="text-xl font-semibold text-white">Manage Roles for: <span className="text-orange-400">{editingRolesApiKey.label}</span></h2>
            <button onClick={() => { setEditingRolesApiKey(null); setShowCreateCustomRoleForm(false); setCreateCustomRoleError(null); }} className="text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200">
              <XCircleIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6 border-b border-gray-200">
            {userCustomRolesError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <p className="text-red-800 text-sm">Error with custom roles: {userCustomRolesError}</p>
              </div>
            )}

            <TierGuard
              feature="custom_roles"
              customMessage="Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases."
            >
              <div className="flex justify-end mb-4">
                <button
                  onClick={() => setShowCreateCustomRoleForm(!showCreateCustomRoleForm)}
                  className="btn-primary text-sm inline-flex items-center"
                >
                  <PlusCircleIcon className="h-4 w-4 mr-2" />
                  {showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'}
                </button>
              </div>

              {showCreateCustomRoleForm && (
                <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4">
                  <h3 className="text-md font-medium text-white mb-3">Create New Custom Role for this Configuration</h3>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="newCustomRoleId" className="block text-sm font-medium text-gray-300 mb-1">Role ID (short, no spaces, max 30 chars)</label>
                      <input
                        type="text" id="newCustomRoleId" value={newCustomRoleId}
                        onChange={(e) => setNewCustomRoleId(e.target.value.replace(/\s/g, ''))}
                        className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        maxLength={30}
                        placeholder="e.g., my_blog_writer"
                      />
                    </div>
                    <div>
                      <label htmlFor="newCustomRoleName" className="block text-sm font-medium text-gray-300 mb-1">Display Name (max 100 chars)</label>
                      <input
                        type="text" id="newCustomRoleName" value={newCustomRoleName}
                        onChange={(e) => setNewCustomRoleName(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        maxLength={100}
                        placeholder="e.g., My Awesome Blog Writer"
                      />
                    </div>
                    <div>
                      <label htmlFor="newCustomRoleDescription" className="block text-sm font-medium text-gray-300 mb-1">Description (optional, max 500 chars)</label>
                      <textarea
                        id="newCustomRoleDescription" value={newCustomRoleDescription}
                        onChange={(e) => setNewCustomRoleDescription(e.target.value)}
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        maxLength={500}
                        placeholder="Optional: Describe what this role is for..."
                      />
                    </div>
                    {createCustomRoleError && (
                      <div className="bg-red-900/50 border border-red-800/50 rounded-lg p-3">
                        <p className="text-red-300 text-sm">{createCustomRoleError}</p>
                      </div>
                    )}
                    <button
                      onClick={handleCreateCustomRole}
                      disabled={isSavingCustomRole}
                      className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'}
                    </button>
                  </div>
                </div>
              )}
            </TierGuard>
          </div>

          <div className="p-6">
            <p className="text-sm font-medium text-gray-300 mb-3">Select roles to assign:</p>
            <div className="overflow-y-auto space-y-2" style={{ maxHeight: 'calc(90vh - 350px)' }}>
              {isLoadingUserCustomRoles && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"></div>
                  <p className="text-gray-400 text-sm ml-2">Loading custom roles...</p>
                </div>
              )}
              {combinedRoles.map(role => {
                const isAssigned = editingRolesApiKey.assigned_roles.some(ar => ar.id === role.id);
                return (
                  <div key={role.id} className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${
                    isAssigned
                      ? 'bg-orange-500/20 border-orange-500/30 shadow-sm'
                      : 'bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm'
                  }`}>
                    <label htmlFor={`role-${role.id}`} className="flex items-center cursor-pointer flex-grow">
                      <input
                        type="checkbox"
                        id={`role-${role.id}`}
                        checked={isAssigned}
                        onChange={() => handleRoleToggle(editingRolesApiKey!, role.id, isAssigned)}
                        className="h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700"
                      />
                      <span className={`ml-3 text-sm font-medium ${isAssigned ? 'text-orange-300' : 'text-white'}`}>
                        {role.name}
                      </span>
                      {role.isCustom && (
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300">
                          Custom
                        </span>
                      )}
                    </label>
                    {role.isCustom && role.databaseId && (
                      <button
                        onClick={() => handleDeleteCustomRole(role.databaseId!, role.name)}
                        disabled={deletingCustomRoleId === role.databaseId}
                        className="p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2"
                        title="Delete this custom role"
                      >
                         {deletingCustomRoleId === role.databaseId ? <Cog6ToothIcon className="h-4 w-4 animate-spin" /> : <TrashIcon className="h-4 w-4" />}
                      </button>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          <div className="p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg">
            <div className="flex justify-end">
              <button
                  onClick={() => { setEditingRolesApiKey(null); setShowCreateCustomRoleForm(false); setCreateCustomRoleError(null); }}
                  className="btn-secondary"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Main render logic with optimistic loading
  if (showOptimisticLoading && !isCached(configId)) {
    return <ManageKeysLoadingSkeleton />;
  }

  if (isLoadingConfig && !configDetails) {
    return <CompactManageKeysLoadingSkeleton />;
  }

  // Browsing configuration handlers
  const handleBrowsingToggle = (enabled: boolean) => {
    setBrowsingEnabled(enabled);
  };

  const handleAddBrowsingModel = () => {
    const defaultProvider = 'openai';
    const modelOptions = getBrowsingModelOptions(defaultProvider);
    const defaultModel = modelOptions.length > 0 ? modelOptions[0].value : '';

    const newModel: BrowsingModel = {
      id: `browsing_${Date.now()}`,
      provider: defaultProvider,
      model: defaultModel,
      api_key: '',
      temperature: 0.7,
      order: browsingModels.length
    };
    setBrowsingModels([...browsingModels, newModel]);
  };

  const handleUpdateBrowsingModel = (index: number, updates: Partial<BrowsingModel>) => {
    const updatedModels = browsingModels.map((model, i) =>
      i === index ? { ...model, ...updates } : model
    );
    setBrowsingModels(updatedModels);
  };

  const handleRemoveBrowsingModel = (index: number) => {
    setBrowsingModels(browsingModels.filter((_, i) => i !== index));
  };

  const handleMoveBrowsingModel = (index: number, direction: 'up' | 'down') => {
    const newModels = [...browsingModels];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;

    if (targetIndex >= 0 && targetIndex < newModels.length) {
      [newModels[index], newModels[targetIndex]] = [newModels[targetIndex], newModels[index]];
      // Update order values
      newModels.forEach((model, i) => {
        model.order = i;
      });
      setBrowsingModels(newModels);
    }
  };

  const handleSaveBrowsingConfig = async () => {
    if (!configId) return;

    // Validate browsing models if browsing is enabled
    if (browsingEnabled) {
      if (browsingModels.length === 0) {
        setError('Please add at least one browsing model when browsing is enabled.');
        return;
      }

      const invalidModels = browsingModels.filter(model =>
        !model.provider || !model.model || !model.api_key.trim()
      );

      if (invalidModels.length > 0) {
        setError('Please fill in all required fields (Provider, Model, API Key) for all browsing models.');
        return;
      }
    }

    setIsSavingBrowsing(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch(`/api/custom-configs/${configId}/browsing`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          browsing_enabled: browsingEnabled,
          browsing_models: browsingModels
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save browsing configuration');
      }

      setSuccessMessage('Browsing configuration saved successfully!');

      // Auto-clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);

      // Update config details to reflect changes
      if (configDetails) {
        setConfigDetails({
          ...configDetails,
          browsing_enabled: browsingEnabled,
          browsing_models: browsingModels
        });
      }

      // Clear cache to ensure fresh data on next load
      clearCache(configId);

    } catch (err: any) {
      setError(`Error saving browsing configuration: ${err.message}`);
    } finally {
      setIsSavingBrowsing(false);
    }
  };

  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <button
            onClick={() => navigateOptimistically('/my-models')}
            className="text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to My API Models
          </button>

          {/* Modern Header Card */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex-1">
              {configDetails ? (
                <>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                      <Cog6ToothIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold text-white">
                        {configDetails.name}
                      </h1>
                      <p className="text-sm text-gray-400 mt-1">Model Configuration</p>
                    </div>
                  </div>
                  <div className="flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit">
                    <span className="inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                    ID: {configDetails.id}
                  </div>
                </>
              ) : error && !isLoadingConfig ? (
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4">
                    <XCircleIcon className="h-6 w-6 text-red-400" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-red-400">Configuration Error</h1>
                    <p className="text-red-300 mt-1">{error.replace("Error loading model configuration: ","")}</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4">
                    <CloudArrowDownIcon className="h-6 w-6 text-gray-400 animate-pulse" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">Loading Configuration...</h1>
                    <p className="text-gray-400 mt-1">Please wait while we fetch your model details</p>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {configDetails && (
              <div className="flex flex-col sm:flex-row gap-3">


                {/* Advanced Routing Setup Button */}
                <button
                  onClick={() => navigateOptimistically(`/routing-setup/${configId}?from=model-config`)}
                  className="inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group"
                  {...createRoutingHoverPrefetch(configId)}
                >
                  <Cog6ToothIcon className="h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200" />
                  Advanced Routing Setup
                </button>
              </div>
            )}
          </div>
        </div>

          {/* Status Messages */}
          {successMessage && (
            <div className="bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in">
              <div className="flex items-center space-x-3">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
                <p className="text-green-300 font-medium">{successMessage}</p>
              </div>
            </div>
          )}
          {error && (
            <div className="bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in">
              <div className="flex items-center space-x-3">
                <XCircleIcon className="h-5 w-5 text-red-400" />
                <p className="text-red-300 font-medium">{error}</p>
              </div>
            </div>
          )}
        </div>

      {configDetails && (
        <div className="space-y-6">
          {/* Tab Navigation */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2">
            <div className="flex space-x-1">
              <button
                onClick={() => setActiveTab('provider-keys')}
                className={`flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                  activeTab === 'provider-keys'
                    ? 'bg-orange-500 text-white shadow-md'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <KeyIcon className="h-4 w-4" />
                  <span>Provider API Keys</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('user-api-keys')}
                className={`flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                  activeTab === 'user-api-keys'
                    ? 'bg-orange-500 text-white shadow-md'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <GlobeAltIcon className="h-4 w-4" />
                  <span>Generated API Keys</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('browsing-config')}
                className={`flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                  activeTab === 'browsing-config'
                    ? 'bg-orange-500 text-white shadow-md'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <GlobeAltIcon className="h-4 w-4" />
                  <span>Browsing Config</span>
                </div>
              </button>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'provider-keys' && (
            <div className="grid grid-cols-1 xl:grid-cols-5 gap-8">
              {/* Left Column - Add New API Key Form */}
              <div className="xl:col-span-2">
                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8">
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md">
                      <PlusIcon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Add Provider API Key</h2>
                      <p className="text-xs text-gray-400">Configure new provider key</p>
                    </div>
                  </div>

              <form onSubmit={handleSaveKey} className="space-y-5">
                <div className="space-y-4">
                  <div>
                    <label htmlFor="provider" className="block text-sm font-medium text-gray-300 mb-2">
                      Provider
                    </label>
                    <select
                      id="provider"
                      value={provider}
                      onChange={(e) => { setProvider(e.target.value); }}
                      className="w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm"
                    >
                      {PROVIDER_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value} className="bg-gray-800 text-white">{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="apiKeyRaw" className="block text-sm font-medium text-gray-300 mb-2">
                      API Key
                    </label>
                    <input
                      id="apiKeyRaw"
                      type="password"
                      value={apiKeyRaw}
                      onChange={(e) => setApiKeyRaw(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm"
                      placeholder="Enter your API key"
                    />
                    {/* Info/Error messages for model fetching */}
                    {isFetchingProviderModels && fetchedProviderModels === null && (
                      <p className="mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg">
                        <CloudArrowDownIcon className="h-4 w-4 mr-1 animate-pulse" />
                        Fetching models...
                      </p>
                    )}
                    {fetchProviderModelsError && (
                      <p className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg">{fetchProviderModelsError}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="predefinedModelId" className="block text-sm font-medium text-gray-300 mb-2">
                      Model Variant
                    </label>
                    <select
                      id="predefinedModelId"
                      value={predefinedModelId}
                      onChange={(e) => setPredefinedModelId(e.target.value)}
                      disabled={!modelOptions.length}
                      className="w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500"
                    >
                      {modelOptions.length > 0 ? (
                        modelOptions.map(m => (
                          <option key={m.value} value={m.value} className="bg-gray-800 text-white">{m.label}</option>
                        ))
                      ) : (
                        <option value="" disabled className="bg-gray-800 text-gray-400">{fetchedProviderModels === null && isFetchingProviderModels ? "Loading models..." : "Select a provider first"}</option>
                      )}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="label" className="block text-sm font-medium text-gray-300 mb-2">
                      Label
                    </label>
                    <input
                      type="text"
                      id="label"
                      value={label}
                      onChange={(e) => setLabel(e.target.value)}
                      required
                      className="w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm"
                      placeholder="e.g., My OpenAI GPT-4o Key #1"
                    />
                  </div>

                  <div>
                    <label htmlFor="temperature" className="block text-sm font-medium text-gray-300 mb-2">
                      Temperature
                      <span className="text-xs text-gray-400 ml-1">(0.0 - 2.0)</span>
                    </label>
                    <div className="space-y-2">
                      <input
                        type="range"
                        id="temperature"
                        min="0"
                        max="2"
                        step="0.1"
                        value={temperature}
                        onChange={(e) => setTemperature(parseFloat(e.target.value))}
                        className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
                      />
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-400">Conservative</span>
                        <div className="flex items-center space-x-2">
                          <input
                            type="number"
                            min="0"
                            max="2"
                            step="0.1"
                            value={temperature}
                            onChange={(e) => setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0)))}
                            className="w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                          />
                        </div>
                        <span className="text-xs text-gray-400">Creative</span>
                      </div>
                      <p className="text-xs text-gray-400">
                        Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative
                      </p>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim()}
                  className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm"
                >
                  {isSavingKey ? (
                    <span className="flex items-center justify-center">
                      <CloudArrowDownIcon className="h-4 w-4 mr-2 animate-pulse" />
                      Saving...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add API Key
                    </span>
                  )}
                </button>
              </form>

              {/* Information about duplicate rules */}
              <div className="mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg">
                <div className="flex items-start space-x-3">
                  <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-300 mb-1">Key Configuration Rules</h4>
                    <div className="text-xs text-blue-200 space-y-1">
                      <p>✅ <strong>Same API key, different models:</strong> Allowed</p>
                      <p>✅ <strong>Different API keys, same model:</strong> Allowed</p>
                      <p>❌ <strong>Same model twice:</strong> Not allowed in one configuration</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - API Keys Management */}
          <div className="xl:col-span-3">
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md">
                  <KeyIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">API Keys & Roles</h2>
                  <p className="text-xs text-gray-400">Manage existing keys</p>
                </div>
              </div>

              {isLoadingKeysAndRoles && (
                <div className="text-center py-8">
                  <CloudArrowDownIcon className="h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse" />
                  <p className="text-gray-400 text-sm">Loading API keys...</p>
                </div>
              )}

              {!isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || (error && error.startsWith("Error loading model configuration:"))) && (
                <div className="text-center py-8">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <KeyIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-1">No API Keys</h3>
                  <p className="text-xs text-gray-500">Add your first key using the form</p>
                </div>
              )}

              {!isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {savedKeysWithRoles.map((key, index) => (
                    <div key={key.id} className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in" style={{animationDelay: `${index * 50}ms`}}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center mb-2">
                            <h3 className="text-sm font-semibold text-white truncate mr-2">{key.label}</h3>
                            {key.is_default_general_chat_model && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0">
                                <ShieldCheckIcon className="h-3 w-3 mr-1" />
                                Default
                              </span>
                            )}
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <p className="text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600">
                                {key.provider} ({key.predefined_model_id})
                              </p>
                              <p className="text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50">
                                Temp: {key.temperature}
                              </p>
                            </div>

                            <TierGuard
                              feature="custom_roles"
                              fallback={
                                <div className="flex flex-wrap gap-1">
                                  <span className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600">
                                    Roles available on Starter plan+
                                  </span>
                                </div>
                              }
                            >
                              <div className="flex flex-wrap gap-1">
                                {key.assigned_roles.length > 0 ? (
                                  key.assigned_roles.map(role => (
                                    <span key={role.id} className="inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50">
                                      {role.name}
                                    </span>
                                  ))
                                ) : (
                                  <span className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600">No roles</span>
                                )}
                              </div>
                            </TierGuard>
                          </div>

                          {!key.is_default_general_chat_model && (
                            <button
                              onClick={() => handleSetDefaultChatKey(key.id)}
                              className="text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors"
                              data-tooltip-id="global-tooltip"
                              data-tooltip-content="Set as default chat model"
                            >
                              Set Default
                            </button>
                          )}
                        </div>

                        <div className="flex items-center space-x-1 ml-2 flex-shrink-0">
                          <button
                            onClick={() => handleEditKey(key)}
                            disabled={isDeletingKey === key.id}
                            className="p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50"
                            data-tooltip-id="global-tooltip"
                            data-tooltip-content="Edit Model & Settings"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <TierGuard
                            feature="custom_roles"
                            fallback={
                              <button
                                disabled
                                className="p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50"
                                data-tooltip-id="global-tooltip"
                                data-tooltip-content="Role management requires Starter plan or higher"
                              >
                                <Cog6ToothIcon className="h-4 w-4" />
                              </button>
                            }
                          >
                            <button
                              onClick={() => setEditingRolesApiKey(key)}
                              disabled={isDeletingKey === key.id}
                              className="p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50"
                              data-tooltip-id="global-tooltip"
                              data-tooltip-content="Manage Roles"
                            >
                              <Cog6ToothIcon className="h-4 w-4" />
                            </button>
                          </TierGuard>
                          <button
                            onClick={() => handleDeleteKey(key.id, key.label)}
                            disabled={isDeletingKey === key.id}
                            className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50"
                            data-tooltip-id="global-tooltip"
                            data-tooltip-content="Delete Key"
                          >
                            {isDeletingKey === key.id ? (
                              <TrashIcon className="h-4 w-4 animate-pulse" />
                            ) : (
                              <TrashIcon className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

            {!isLoadingKeysAndRoles && error && !error.startsWith("Error loading model configuration:") && (
              <div className="bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <XCircleIcon className="h-5 w-5 text-red-400" />
                  <p className="text-red-300 font-medium text-sm">Could not load API keys/roles: {error}</p>
                </div>
              </div>
            )}
            </div>
          </div>
        </div>
      )}

        {/* User-Generated API Keys Tab */}
        {activeTab === 'user-api-keys' && (
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6">
            <ApiKeyManager
              configId={configId}
              configName={configDetails.name}
            />
          </div>
        )}

        {/* Browsing Configuration Tab */}
        {activeTab === 'browsing-config' && (
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md">
                <GlobeAltIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Browsing Configuration</h2>
                <p className="text-xs text-gray-400">Configure web browsing capabilities</p>
              </div>
            </div>

            {/* Browsing Toggle */}
            <div className="mb-6">
              <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
                <div>
                  <h3 className="text-white font-medium">Enable Web Browsing</h3>
                  <p className="text-gray-400 text-sm">Allow this configuration to browse the web for real-time information</p>
                </div>
                <button
                  onClick={() => handleBrowsingToggle(!browsingEnabled)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                    browsingEnabled ? 'bg-orange-500' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                      browsingEnabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* Browsing Configuration Status Messages */}
            {successMessage && activeTab === 'browsing-config' && (
              <div className="bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in">
                <div className="flex items-center space-x-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-400" />
                  <p className="text-green-300 font-medium">{successMessage}</p>
                </div>
              </div>
            )}
            {error && activeTab === 'browsing-config' && (
              <div className="bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in">
                <div className="flex items-center space-x-3">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <p className="text-red-300 font-medium">{error}</p>
                </div>
              </div>
            )}

            {/* Browsing Models Configuration */}
            {browsingEnabled && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-white font-medium">Browsing Models</h3>
                  <button
                    onClick={handleAddBrowsingModel}
                    className="px-3 py-2 bg-orange-500 hover:bg-orange-600 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center space-x-2"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span>Add Model</span>
                  </button>
                </div>

                {browsingModels.length === 0 ? (
                  <div className="text-center py-8 text-gray-400">
                    <GlobeAltIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>No browsing models configured</p>
                    <p className="text-sm">Add a model to enable web browsing</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {browsingModels.map((model, index) => (
                      <div key={model.id} className="p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-white font-medium">Model {index + 1}</span>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleMoveBrowsingModel(index, 'up')}
                              disabled={index === 0}
                              className="p-1 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              ↑
                            </button>
                            <button
                              onClick={() => handleMoveBrowsingModel(index, 'down')}
                              disabled={index === browsingModels.length - 1}
                              className="p-1 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              ↓
                            </button>
                            <button
                              onClick={() => handleRemoveBrowsingModel(index)}
                              className="p-1 text-red-400 hover:text-red-300"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-1">Provider</label>
                            <select
                              value={model.provider}
                              onChange={(e) => {
                                const newProvider = e.target.value;
                                const modelOptions = getBrowsingModelOptions(newProvider);
                                handleUpdateBrowsingModel(index, {
                                  provider: newProvider,
                                  model: modelOptions.length > 0 ? modelOptions[0].value : ''
                                });
                              }}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
                            >
                              {PROVIDER_OPTIONS.map(option => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-1">Model</label>
                            <select
                              value={model.model}
                              onChange={(e) => handleUpdateBrowsingModel(index, { model: e.target.value })}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
                            >
                              {getBrowsingModelOptions(model.provider).length === 0 ? (
                                <option value="">Loading models...</option>
                              ) : (
                                getBrowsingModelOptions(model.provider).map(option => (
                                  <option key={option.value} value={option.value}>
                                    {option.label}
                                  </option>
                                ))
                              )}
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-1">API Key</label>
                            <input
                              type="password"
                              value={model.api_key}
                              onChange={(e) => handleUpdateBrowsingModel(index, { api_key: e.target.value })}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
                              placeholder="Enter API key"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-1">Temperature</label>
                            <input
                              type="number"
                              min="0"
                              max="2"
                              step="0.1"
                              value={model.temperature || 0.7}
                              onChange={(e) => handleUpdateBrowsingModel(index, { temperature: parseFloat(e.target.value) })}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Save Button */}
                <div className="flex justify-end pt-4">
                  <button
                    onClick={handleSaveBrowsingConfig}
                    disabled={isSavingBrowsing}
                    className="px-6 py-2 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-600 text-white font-medium rounded-lg transition-colors duration-200 flex items-center space-x-2"
                  >
                    {isSavingBrowsing ? (
                      <>
                        <Cog6ToothIcon className="h-4 w-4 animate-spin" />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="h-4 w-4" />
                        <span>Save Configuration</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
    </div>
  )}

      {/* Modal for Editing Roles */}
      {editingRolesApiKey && renderManageRolesModal()}

      {/* Modal for Editing API Key */}
      {editingApiKey && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg">
            <div className="flex justify-between items-center p-6 border-b border-gray-800/50">
              <h2 className="text-xl font-semibold text-white">Edit API Key</h2>
              <button
                onClick={() => setEditingApiKey(null)}
                className="text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <h3 className="text-lg font-medium text-white mb-2">{editingApiKey.label}</h3>
                <p className="text-sm text-gray-400">
                  Current: {editingApiKey.provider} ({editingApiKey.predefined_model_id})
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Provider
                  </label>
                  <div className="w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300">
                    {llmProviders.find(p => p.id === editingApiKey.provider)?.name || editingApiKey.provider}
                  </div>
                  <p className="text-xs text-gray-400 mt-1">Provider cannot be changed</p>
                </div>

                <div>
                  <label htmlFor="editModelId" className="block text-sm font-medium text-gray-300 mb-2">
                    Model
                  </label>
                  <select
                    id="editModelId"
                    value={editPredefinedModelId}
                    onChange={(e) => setEditPredefinedModelId(e.target.value)}
                    disabled={!editModelOptions.length}
                    className="w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30"
                  >
                    {editModelOptions.length > 0 ? (
                      editModelOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>
                        {isFetchingProviderModels ? 'Loading models...' : 'No models available'}
                      </option>
                    )}
                  </select>
                </div>

                <div>
                  <label htmlFor="editTemperature" className="block text-sm font-medium text-gray-300 mb-2">
                    Temperature: {editTemperature}
                  </label>
                  <input
                    type="range"
                    id="editTemperature"
                    min="0"
                    max="2"
                    step="0.1"
                    value={editTemperature}
                    onChange={(e) => setEditTemperature(parseFloat(e.target.value))}
                    className="slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>0.0 (Focused)</span>
                    <span>1.0 (Balanced)</span>
                    <span>2.0 (Creative)</span>
                  </div>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-3">
                  <p className="text-xs text-gray-400">
                    You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingApiKey(null)}
                  className="btn-secondary"
                  disabled={isSavingEdit}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEdit}
                  disabled={isSavingEdit}
                  className="btn-primary"
                >
                  {isSavingEdit ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {!configDetails && !isLoadingConfig && !error && (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8">
          <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <InformationCircleIcon className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">Model Not Found</h3>
          <p className="text-sm text-gray-600 mb-8">This API Model configuration could not be found or may have been deleted.</p>
          <button
            onClick={() => navigateOptimistically('/my-models')}
            className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Return to My API Models
          </button>
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />

        <Tooltip id="global-tooltip" />
      </div>
    </div>
  );
}