// Browsing Detection Service - Determines when user queries require web browsing
// Similar to RAG detection but for real-time web information needs

interface BrowsingDetectionResult {
  shouldBrowse: boolean;
  refinedQuery?: string;
  browsingType?: 'search' | 'navigate' | 'extract';
  confidence?: number;
}

export class BrowsingDetectionService {
  private static instance: BrowsingDetectionService;
  private classificationApiKey: string | null = null;

  constructor() {
    this.classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY || null;
  }

  static getInstance(): BrowsingDetectionService {
    if (!BrowsingDetectionService.instance) {
      BrowsingDetectionService.instance = new BrowsingDetectionService();
    }
    return BrowsingDetectionService.instance;
  }

  /**
   * Determine if a user query requires web browsing for real-time information
   */
  async shouldTriggerBrowsing(
    query: string,
    conversationContext?: string
  ): Promise<BrowsingDetectionResult> {
    try {
      // Skip very short queries immediately
      if (query.trim().length < 8) {
        return { shouldBrowse: false };
      }

      if (!this.classificationApiKey) {
        console.warn('[Browsing Detection] No classification API key, using fallback logic');
        return this.fallbackDetection(query);
      }

      const classificationPrompt = `You are an intelligent browsing classifier. Analyze if this user query requires real-time web browsing to get current information.

USER QUERY: "${query}"

${conversationContext ? `CONVERSATION CONTEXT: ${conversationContext}` : ''}

CLASSIFICATION CRITERIA:
✅ REQUIRES BROWSING (YES) when:
- Query asks for CURRENT/RECENT information (news, events, prices, weather, etc.)
- Query mentions specific dates, "today", "now", "latest", "current", "recent"
- Query asks about live data (stock prices, exchange rates, sports scores)
- Query asks about current events, breaking news, or recent developments
- Query asks about website content, specific URLs, or online resources
- Query asks "what's happening", "latest news about", "current status of"
- Query asks about real-time information that changes frequently
- Query asks about specific companies' current status, recent announcements
- Query asks about weather, traffic, or location-based current information
- Query asks about availability, pricing, or current offers from websites
- Query asks about recent social media posts, trends, or viral content
- Query asks about current job listings, real estate, or market data
- Query asks about live events, schedules, or current availability

❌ DOESN'T REQUIRE BROWSING (NO) when:
- General knowledge questions about historical facts, definitions, concepts
- Questions about well-established information that doesn't change
- Creative writing, coding help, math problems, or educational content
- Personal advice, opinions, or subjective questions
- Questions about basic facts, scientific principles, or common knowledge
- Simple greetings, thanks, or conversational responses
- Questions that can be answered with existing AI knowledge
- Questions about famous people's basic biographical information
- Questions about established theories, concepts, or principles

EXAMPLES:
- "What's the current price of Bitcoin?" → YES|SEARCH|current Bitcoin price USD value today
- "What's happening in Ukraine today?" → YES|SEARCH|Ukraine news today current events latest developments
- "Check the weather in New York" → YES|SEARCH|New York weather today current forecast
- "What is machine learning?" → NO (general knowledge)
- "Help me write a poem" → NO (creative task)
- "What's 2+2?" → NO (basic math)

RESPONSE FORMAT:
If YES: "YES|BROWSING_TYPE|refined_search_query"
If NO: "NO"

BROWSING_TYPE options:
- SEARCH: For general web search queries
- NAVIGATE: For specific website/URL content
- EXTRACT: For extracting specific data from known sources

SEARCH QUERY REFINEMENT RULES:
- Remove conversational words like "tell me", "what's", "please", etc.
- Focus on key terms and current/recent indicators
- Include time-sensitive terms like "today", "current", "latest", "recent"
- Make queries specific and search-engine friendly`;

      const classificationPayload = {
        model: 'gemini-2.0-flash-001',
        messages: [
          {
            role: 'user',
            content: classificationPrompt
          }
        ],
        temperature: 0.1,
        max_tokens: 150
      };

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.classificationApiKey}`,
        },
        body: JSON.stringify(classificationPayload),
        signal: AbortSignal.timeout(3000) // Quick 3s timeout for classification
      });

      if (!response.ok) {
        console.warn('[Browsing Detection] Classification failed, using fallback');
        return this.fallbackDetection(query);
      }

      const result = await response.json();
      const decision = result.choices?.[0]?.message?.content?.trim();

      if (decision?.startsWith('YES|')) {
        const parts = decision.substring(4).split('|');
        const browsingType = parts[0]?.toLowerCase() as 'search' | 'navigate' | 'extract';
        const refinedQuery = parts[1]?.trim();
        
        console.log(`[Browsing Detection] Gemini decision for "${query.substring(0, 50)}...": YES (BROWSE) - Type: ${browsingType}, Query: "${refinedQuery}"`);
        
        return { 
          shouldBrowse: true, 
          refinedQuery: refinedQuery || query,
          browsingType: browsingType || 'search',
          confidence: 0.9
        };
      } else if (decision?.startsWith('NO')) {
        console.log(`[Browsing Detection] Gemini decision for "${query.substring(0, 50)}...": NO (SKIP) - AI can handle this`);
        return { shouldBrowse: false, confidence: 0.9 };
      } else {
        // Fallback for unexpected response format
        console.warn(`[Browsing Detection] Unexpected decision format: "${decision}", using fallback`);
        return this.fallbackDetection(query);
      }

    } catch (error) {
      console.warn('[Browsing Detection] Classification error, using fallback:', error);
      return this.fallbackDetection(query);
    }
  }

  /**
   * Fallback detection logic when AI classification is unavailable
   */
  private fallbackDetection(query: string): BrowsingDetectionResult {
    const lowerQuery = query.toLowerCase();
    
    // Time-sensitive keywords
    const timeKeywords = ['current', 'today', 'now', 'latest', 'recent', 'live', 'real-time', 'breaking'];
    const hasTimeKeywords = timeKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Information type keywords
    const infoKeywords = ['news', 'price', 'weather', 'stock', 'rate', 'score', 'status', 'update'];
    const hasInfoKeywords = infoKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Question patterns that typically need browsing
    const browsingPatterns = [
      /what'?s happening/i,
      /what'?s the (current|latest)/i,
      /check the/i,
      /find out/i,
      /look up/i
    ];
    const matchesPattern = browsingPatterns.some(pattern => pattern.test(query));
    
    const shouldBrowse = hasTimeKeywords || hasInfoKeywords || matchesPattern;
    
    if (shouldBrowse) {
      console.log(`[Browsing Detection] Fallback decision for "${query.substring(0, 50)}...": YES (BROWSE) - Pattern match`);
      return { 
        shouldBrowse: true, 
        refinedQuery: query,
        browsingType: 'search',
        confidence: 0.6
      };
    } else {
      console.log(`[Browsing Detection] Fallback decision for "${query.substring(0, 50)}...": NO (SKIP) - No browsing patterns`);
      return { shouldBrowse: false, confidence: 0.6 };
    }
  }
}

export default BrowsingDetectionService;
