"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately";
exports.ids = ["vendor-chunks/@react-stately"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-stately/flags/dist/import.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@react-stately/flags/dist/import.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableShadowDOM: () => (/* binding */ $f4e2df6bd15f8569$export$12b151d9882e9985),\n/* harmony export */   enableTableNestedRows: () => (/* binding */ $f4e2df6bd15f8569$export$d9d8a0f82de49530),\n/* harmony export */   shadowDOM: () => (/* binding */ $f4e2df6bd15f8569$export$98658e8c59125e6a),\n/* harmony export */   tableNestedRows: () => (/* binding */ $f4e2df6bd15f8569$export$1b00cb14a96194e6)\n/* harmony export */ });\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ let $f4e2df6bd15f8569$var$_tableNestedRows = false;\nlet $f4e2df6bd15f8569$var$_shadowDOM = false;\nfunction $f4e2df6bd15f8569$export$d9d8a0f82de49530() {\n    $f4e2df6bd15f8569$var$_tableNestedRows = true;\n}\nfunction $f4e2df6bd15f8569$export$1b00cb14a96194e6() {\n    return $f4e2df6bd15f8569$var$_tableNestedRows;\n}\nfunction $f4e2df6bd15f8569$export$12b151d9882e9985() {\n    $f4e2df6bd15f8569$var$_shadowDOM = true;\n}\nfunction $f4e2df6bd15f8569$export$98658e8c59125e6a() {\n    return $f4e2df6bd15f8569$var$_shadowDOM;\n}\n\n\n\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-stately/flags/dist/import.mjs\n");

/***/ })

};
;