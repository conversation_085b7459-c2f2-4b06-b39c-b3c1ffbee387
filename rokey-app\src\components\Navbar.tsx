'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { UserCircleIcon, BellIcon, Cog6ToothIcon, Bars3Icon, ArrowRightOnRectangleIcon, ChevronDownIcon, MagnifyingGlassIcon, CreditCardIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { useSidebar } from '@/contexts/SidebarContext';
import { usePathname } from 'next/navigation';
import { useSubscription } from '@/hooks/useSubscription';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { GlobalSearch } from './GlobalSearch';

export default function Navbar() {
  const { isCollapsed, isHovered, toggleSidebar } = useSidebar();
  const pathname = usePathname();
  const { user, subscriptionStatus } = useSubscription();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const supabase = createSupabaseBrowserClient();

  // Track if we're on desktop (lg breakpoint and above)
  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024); // lg breakpoint
    };

    // Check on mount
    checkIsDesktop();

    // Listen for resize events
    window.addEventListener('resize', checkIsDesktop);
    return () => window.removeEventListener('resize', checkIsDesktop);
  }, []);

  // Get user display info
  const firstName = user?.user_metadata?.first_name || user?.user_metadata?.full_name?.split(' ')[0] || 'User';
  const initials = firstName.charAt(0).toUpperCase() + (user?.user_metadata?.last_name?.charAt(0)?.toUpperCase() || firstName.charAt(1)?.toUpperCase() || 'U');

  // Get simple breadcrumb info from pathname
  const getBreadcrumb = (path: string) => {
    switch (path) {
      case '/dashboard':
        return { title: 'Dashboard', subtitle: 'Overview & analytics' };
      case '/playground':
        return { title: 'Playground', subtitle: 'Test your models' };
      case '/my-models':
        return { title: 'My Models', subtitle: 'API key management' };
      case '/routing-setup':
        return { title: 'Routing Setup', subtitle: 'Configure routing' };
      // Manual Build temporarily disabled for launch
      // case '/manual-build':
      //   return { title: 'Manual Build', subtitle: 'Visual workflow builder' };
      case '/tools':
        return { title: 'Tool Connections', subtitle: 'Manage connected tools' };
      case '/logs':
        return { title: 'Logs', subtitle: 'Request history' };
      case '/training':
        return { title: 'Prompt Engineering', subtitle: 'Custom prompts' };
      case '/analytics':
        return { title: 'Analytics', subtitle: 'Advanced insights' };
      case '/add-keys':
        return { title: 'Add Keys', subtitle: 'API key setup' };
      default:
        return { title: 'Dashboard', subtitle: 'Overview' };
    }
  };

  const breadcrumb = getBreadcrumb(pathname);

  // Display correct subscription tier name
  const planName = subscriptionStatus?.tier === 'free' ? 'Free Plan' :
                   subscriptionStatus?.tier === 'starter' ? 'Starter Plan' :
                   subscriptionStatus?.tier === 'professional' ? 'Professional Plan' :
                   subscriptionStatus?.tier === 'enterprise' ? 'Enterprise Plan' :
                   'Free Plan'; // Default to Free Plan

  const handleSignOut = async () => {
    try {
      // Clear all caches and storage before signing out
      const { clearAllUserCache } = await import('@/utils/clearUserCache');
      await clearAllUserCache();

      await supabase.auth.signOut();

      // Force a hard reload to clear any remaining cached data
      window.location.href = '/auth/signin';
    } catch (err) {
      console.error('Sign out error:', err);
      // Even if sign out fails, clear caches and redirect
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (clearError) {
        console.warn('Failed to clear storage on error:', clearError);
      }
      window.location.href = '/auth/signin';
    }
  };

  // Handle search keyboard shortcut
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsSearchOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
  return (
    <nav className="header border-b border-gray-800/50 bg-[#040716] backdrop-blur-sm w-full">
      <div className={`px-4 sm:px-6 lg:px-8 ${
        // When sidebar is expanded, use standard max width with centering
        // When sidebar is collapsed, use full width with padding
        isDesktop && (!isCollapsed || isHovered)
          ? 'max-w-7xl mx-auto'
          : isDesktop
            ? 'max-w-none'
            : 'max-w-7xl mx-auto'
      }`}>
        <div className="flex justify-between items-center h-16">
          {/* Left side - Mobile menu button and breadcrumb */}
          <div className="flex items-center space-x-4">
            {/* Mobile menu button - visible on mobile only */}
            <button
              onClick={toggleSidebar}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
              title="Toggle sidebar"
            >
              <Bars3Icon className="h-6 w-6 text-gray-400" />
            </button>

            {/* Mobile logo - visible on mobile only */}
            <div className="lg:hidden">
              <h1 className="text-xl font-bold text-white">RouKey</h1>
            </div>

            {/* Dynamic Breadcrumb - hidden on mobile */}
            <div className="hidden lg:flex items-center space-x-2 text-sm text-gray-400">
              <span>{breadcrumb.title}</span>
              <span>/</span>
              <span className="text-white font-medium">{breadcrumb.subtitle}</span>
            </div>
          </div>

          {/* Right side - responsive */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Search - Hidden on mobile, visible on larger screens */}
            <div className="hidden xl:block relative">
              <button
                onClick={() => setIsSearchOpen(true)}
                className="w-64 pl-10 pr-4 py-2.5 text-sm bg-gray-800/50 border border-gray-700 rounded-lg text-gray-400 hover:text-gray-200 hover:border-gray-600 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200 text-left flex items-center justify-between"
              >
                <span>Search...</span>
                <kbd className="hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-500 bg-gray-700 border border-gray-600 rounded">
                  ⌘K
                </kbd>
              </button>
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-500" />
              </div>
            </div>

            {/* Mobile Search Button */}
            <button
              onClick={() => setIsSearchOpen(true)}
              className="xl:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
            >
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </button>

            {/* Notifications - Always visible */}
            <button className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 relative">
              <BellIcon className="h-5 w-5 text-gray-400" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"></span>
            </button>

            {/* Settings Dropdown - Hidden on mobile */}
            <div className="hidden sm:block relative">
              <button
                onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 flex items-center space-x-1"
              >
                <Cog6ToothIcon className="h-5 w-5 text-gray-400" />
                <ChevronDownIcon className={`h-3 w-3 text-gray-400 transition-transform duration-200 ${isSettingsOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Settings Dropdown Menu */}
              {isSettingsOpen && (
                <>
                  {/* Backdrop */}
                  <div
                    className="fixed inset-0 z-10"
                    onClick={() => setIsSettingsOpen(false)}
                  />

                  {/* Dropdown */}
                  <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-1 z-20">
                    <Link
                      href="/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200"
                      onClick={() => setIsSettingsOpen(false)}
                    >
                      <Cog6ToothIcon className="h-4 w-4 mr-3 text-gray-400" />
                      Account Settings
                    </Link>

                    <Link
                      href="/billing"
                      className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200"
                      onClick={() => setIsSettingsOpen(false)}
                    >
                      <CreditCardIcon className="h-4 w-4 mr-3 text-gray-400" />
                      Billing & Plans
                    </Link>

                    <Link
                      href="/docs"
                      className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200"
                      onClick={() => setIsSettingsOpen(false)}
                    >
                      <DocumentTextIcon className="h-4 w-4 mr-3 text-gray-400" />
                      Documentation
                    </Link>

                    <hr className="my-1 border-gray-700" />

                    <button
                      onClick={handleSignOut}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-400 hover:bg-red-900/20 transition-colors duration-200"
                    >
                      <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3 text-red-400" />
                      Sign Out
                    </button>
                  </div>
                </>
              )}
            </div>

            {/* User Profile - Responsive */}
            <div className="flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 cursor-pointer">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center">
                <span className="text-white font-semibold text-sm">{initials}</span>
              </div>
              <div className="hidden md:block">
                <p className="text-sm font-medium text-white">{firstName}</p>
                <p className="text-xs text-gray-400">{planName}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Global Search Modal */}
      <GlobalSearch
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </nav>
  );
}