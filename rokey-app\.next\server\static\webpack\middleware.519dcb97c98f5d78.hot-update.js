"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Skip middleware for static files, API routes, and Next.js internal routes\n    const pathname = req.nextUrl.pathname;\n    if (pathname.startsWith('/_next/static') || pathname.startsWith('/_next/image') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.includes('.')) {\n        return res;\n    }\n    // Block workflow-related API routes (temporarily disabled for launch)\n    if (pathname.startsWith('/api/manual-build') || pathname.startsWith('/api/workflows') || pathname.startsWith('/api/workflow/')) {\n        console.log('🚫 MIDDLEWARE: Blocking access to workflow API routes (temporarily disabled for launch)');\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: 'Manual Build workflows are temporarily unavailable. Please use router configurations instead.'\n        }, {\n            status: 503\n        });\n    }\n    // Skip other API routes\n    if (pathname.startsWith('/api/')) {\n        return res;\n    }\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('🔥 MIDDLEWARE: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname (already defined above)\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks',\n        // Policy and informational pages\n        '/terms',\n        '/privacy',\n        '/cookies',\n        '/security',\n        '/about',\n        '/contact',\n        '/features',\n        '/docs',\n        '/blog',\n        '/routing-strategies'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing',\n        '/api/external'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        // Special case for root route - only match exactly\n        if (route === '/') {\n            return pathname === '/';\n        }\n        // For other routes, allow exact match or startsWith\n        return pathname === route || pathname.startsWith(route);\n    });\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the user with error handling for network issues (more secure than getSession)\n    let session = null;\n    try {\n        const { data: { user } } = await supabase.auth.getUser();\n        // Create a session-like object for compatibility\n        session = user ? {\n            user\n        } : null;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // Block access to manual-build and workflow routes (temporarily disabled for launch)\n    if (pathname.startsWith('/manual-build') || pathname.startsWith('/playground/workflows')) {\n        console.log('🚫 MIDDLEWARE: Blocking access to workflow routes (temporarily disabled for launch)');\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/dashboard', req.url));\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier, user_status').eq('id', session.user.id).single();\n            let isNoProfileError = false;\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // Check if this is a \"no rows\" error (user has no profile) vs a real network error\n                if (profileError.code === 'PGRST116') {\n                    // This means no profile exists - treat as no profile case\n                    isNoProfileError = true;\n                } else {\n                    // Real network error - allow access to prevent app from being blocked\n                    return res;\n                }\n            }\n            // Check if user has pending status (cannot access protected routes)\n            if (profile && profile.user_status === 'pending') {\n                // User exists but has pending status - redirect to complete payment\n                const userPlan = session.user.user_metadata?.plan || profile.subscription_tier;\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('message', 'complete_payment');\n                redirectUrl.searchParams.set('plan', userPlan);\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n            // If no profile exists (either !profile or PGRST116 error), check if this is a pending payment user\n            if (!profile || isNoProfileError) {\n                // Check if user has pending payment status in their metadata (legacy check)\n                const paymentStatus = session.user.user_metadata?.payment_status;\n                const userPlan = session.user.user_metadata?.plan;\n                if (userPlan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(userPlan)) {\n                    // This user was created for a paid plan - they shouldn't be signed in!\n                    console.log('Middleware: User with paid plan metadata is signed in - this should not happen!');\n                    console.log('Middleware: User plan:', userPlan, 'Payment status:', paymentStatus);\n                    // Sign them out and redirect to pricing\n                    await supabase.auth.signOut();\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('message', 'account_created_complete_payment');\n                    redirectUrl.searchParams.set('plan', userPlan);\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n                if (paymentStatus === 'pending' && userPlan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(userPlan)) {\n                    // This is a user who signed up for a paid plan but hasn't completed payment\n                    // Redirect to pricing page for fresh signup process\n                    const redirectUrl = new URL('/pricing', req.url);\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n                // Only create free profiles for users who don't have pending payments and no paid plan metadata\n                console.log('Middleware: Creating default free profile for user:', session.user.id);\n                try {\n                    const { error: createError } = await supabase.from('user_profiles').insert({\n                        id: session.user.id,\n                        full_name: session.user.user_metadata?.full_name || '',\n                        subscription_tier: 'free',\n                        subscription_status: 'active',\n                        user_status: 'active',\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    });\n                    if (createError) {\n                        console.error('Middleware: Error creating user profile:', createError);\n                        // If we can't create profile, redirect to pricing to be safe\n                        const redirectUrl = new URL('/pricing', req.url);\n                        redirectUrl.searchParams.set('checkout', 'true');\n                        redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                    }\n                    // Profile created successfully, allow access\n                    console.log('Middleware: Successfully created free profile for user:', session.user.id);\n                    return res;\n                } catch (error) {\n                    console.error('Middleware: Exception creating user profile:', error);\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('checkout', 'true');\n                    redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n            }\n            // Check subscription status - free tier users should always have access\n            // For free tier, we don't require subscription_status to be 'active' since they don't have paid subscriptions\n            const hasActiveSubscription = profile.subscription_status === 'active' || profile.subscription_tier === 'free';\n            if (!hasActiveSubscription) {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});