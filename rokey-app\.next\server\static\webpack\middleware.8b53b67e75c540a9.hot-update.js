"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Skip middleware for static files, API routes, and Next.js internal routes\n    const pathname = req.nextUrl.pathname;\n    if (pathname.startsWith('/_next/static') || pathname.startsWith('/_next/image') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.startsWith('/api/') || // Skip all API routes\n    pathname.includes('.')) {\n        return res;\n    }\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('🔥 MIDDLEWARE: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname (already defined above)\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks',\n        // Policy and informational pages\n        '/terms',\n        '/privacy',\n        '/cookies',\n        '/security',\n        '/about',\n        '/contact',\n        '/features',\n        '/docs',\n        '/blog',\n        '/routing-strategies'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing',\n        '/api/external'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        // Special case for root route - only match exactly\n        if (route === '/') {\n            return pathname === '/';\n        }\n        // For other routes, allow exact match or startsWith\n        return pathname === route || pathname.startsWith(route);\n    });\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the user with error handling for network issues (more secure than getSession)\n    let session = null;\n    try {\n        const { data: { user } } = await supabase.auth.getUser();\n        // Create a session-like object for compatibility\n        session = user ? {\n            user\n        } : null;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // Block access to manual-build routes (temporarily disabled for launch)\n    if (pathname.startsWith('/manual-build')) {\n        console.log('🚫 MIDDLEWARE: Blocking access to manual-build routes (temporarily disabled for launch)');\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/dashboard', req.url));\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier, user_status').eq('id', session.user.id).single();\n            let isNoProfileError = false;\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // Check if this is a \"no rows\" error (user has no profile) vs a real network error\n                if (profileError.code === 'PGRST116') {\n                    // This means no profile exists - treat as no profile case\n                    isNoProfileError = true;\n                } else {\n                    // Real network error - allow access to prevent app from being blocked\n                    return res;\n                }\n            }\n            // Check if user has pending status (cannot access protected routes)\n            if (profile && profile.user_status === 'pending') {\n                // User exists but has pending status - redirect to complete payment\n                const userPlan = session.user.user_metadata?.plan || profile.subscription_tier;\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('message', 'complete_payment');\n                redirectUrl.searchParams.set('plan', userPlan);\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n            // If no profile exists (either !profile or PGRST116 error), check if this is a pending payment user\n            if (!profile || isNoProfileError) {\n                // Check if user has pending payment status in their metadata (legacy check)\n                const paymentStatus = session.user.user_metadata?.payment_status;\n                const userPlan = session.user.user_metadata?.plan;\n                if (userPlan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(userPlan)) {\n                    // This user was created for a paid plan - they shouldn't be signed in!\n                    console.log('Middleware: User with paid plan metadata is signed in - this should not happen!');\n                    console.log('Middleware: User plan:', userPlan, 'Payment status:', paymentStatus);\n                    // Sign them out and redirect to pricing\n                    await supabase.auth.signOut();\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('message', 'account_created_complete_payment');\n                    redirectUrl.searchParams.set('plan', userPlan);\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n                if (paymentStatus === 'pending' && userPlan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(userPlan)) {\n                    // This is a user who signed up for a paid plan but hasn't completed payment\n                    // Redirect to pricing page for fresh signup process\n                    const redirectUrl = new URL('/pricing', req.url);\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n                // Only create free profiles for users who don't have pending payments and no paid plan metadata\n                console.log('Middleware: Creating default free profile for user:', session.user.id);\n                try {\n                    const { error: createError } = await supabase.from('user_profiles').insert({\n                        id: session.user.id,\n                        full_name: session.user.user_metadata?.full_name || '',\n                        subscription_tier: 'free',\n                        subscription_status: 'active',\n                        user_status: 'active',\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    });\n                    if (createError) {\n                        console.error('Middleware: Error creating user profile:', createError);\n                        // If we can't create profile, redirect to pricing to be safe\n                        const redirectUrl = new URL('/pricing', req.url);\n                        redirectUrl.searchParams.set('checkout', 'true');\n                        redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                    }\n                    // Profile created successfully, allow access\n                    console.log('Middleware: Successfully created free profile for user:', session.user.id);\n                    return res;\n                } catch (error) {\n                    console.error('Middleware: Exception creating user profile:', error);\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('checkout', 'true');\n                    redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n            }\n            // Check subscription status - free tier users should always have access\n            // For free tier, we don't require subscription_status to be 'active' since they don't have paid subscriptions\n            const hasActiveSubscription = profile.subscription_status === 'active' || profile.subscription_tier === 'free';\n            if (!hasActiveSubscription) {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});