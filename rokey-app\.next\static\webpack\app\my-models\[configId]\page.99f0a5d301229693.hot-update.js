"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/my-models/[configId]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/my-models/[configId]/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfigDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useManageKeysPrefetch */ \"(app-pages-browser)/./src/hooks/useManageKeysPrefetch.ts\");\n/* harmony import */ var _components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ManageKeysLoadingSkeleton */ \"(app-pages-browser)/./src/components/ManageKeysLoadingSkeleton.tsx\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/UserApiKeys/ApiKeyManager */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n // For accessing route params\n // Ensure path is correct\n\n\n\n\n\n\n\n\n\n\n\n// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction ConfigDetailsPage() {\n    var _PROVIDER_OPTIONS_, _llmProviders_find;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const configId = params.configId;\n    // Confirmation modal hook\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation)();\n    // Navigation hook with safe context\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe)();\n    const navigateOptimistically = (navigationContext === null || navigationContext === void 0 ? void 0 : navigationContext.navigateOptimistically) || ((href)=>{\n        window.location.href = href;\n    });\n    // Prefetch hooks\n    const { getCachedData, isCached, clearCache } = (0,_hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch)();\n    const { createHoverPrefetch: createRoutingHoverPrefetch } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch)();\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingConfig, setIsLoadingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Provider state now stores the slug (p.id)\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai'); // Stores slug\n    const [predefinedModelId, setPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [apiKeyRaw, setApiKeyRaw] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [label, setLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [temperature, setTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [isSavingKey, setIsSavingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for dynamic model fetching\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [savedKeysWithRoles, setSavedKeysWithRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeletingKey, setIsDeletingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingRolesApiKey, setEditingRolesApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for editing API keys\n    const [editingApiKey, setEditingApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTemperature, setEditTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [editPredefinedModelId, setEditPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingEdit, setIsSavingEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for User-Defined Custom Roles\n    const [userCustomRoles, setUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCustomRolesError, setUserCustomRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newCustomRoleId, setNewCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleName, setNewCustomRoleName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleDescription, setNewCustomRoleDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingCustomRole, setIsSavingCustomRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createCustomRoleError, setCreateCustomRoleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCustomRoleId, setDeletingCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // stores the DB ID (UUID) of the custom role\n    // State for tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('provider-keys');\n    // Browsing configuration state\n    const [browsingEnabled, setBrowsingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [browsingModels, setBrowsingModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSavingBrowsing, setIsSavingBrowsing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch config details with optimistic loading\n    const fetchConfigDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchConfigDetails]\": async ()=>{\n            if (!configId) return;\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached config data for: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                setIsLoadingConfig(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoadingConfig(true);\n            setError(null);\n            try {\n                const res = await fetch(\"/api/custom-configs\");\n                if (!res.ok) {\n                    const errData = await res.json();\n                    throw new Error(errData.error || 'Failed to fetch configurations list');\n                }\n                const allConfigs = await res.json();\n                const currentConfig = allConfigs.find({\n                    \"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\": (c)=>c.id === configId\n                }[\"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\"]);\n                if (!currentConfig) throw new Error('Configuration not found in the list.');\n                setConfigDetails(currentConfig);\n            } catch (err) {\n                setError(\"Error loading model configuration: \".concat(err.message));\n                setConfigDetails(null);\n            } finally{\n                setIsLoadingConfig(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchConfigDetails]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            fetchConfigDetails();\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        fetchConfigDetails\n    ]);\n    // New: Function to fetch all models from the database with caching\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.models) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached models data for: \".concat(configId));\n                setFetchedProviderModels(cachedData.models);\n                setIsFetchingProviderModels(false);\n                return;\n            }\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                // The new API doesn't need a specific provider or API key in the body to list models\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                setFetchProviderModelsError(\"Error fetching models: \".concat(err.message));\n                setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\"], [\n        configId,\n        getCachedData\n    ]);\n    // New: Fetch all models from DB when configId is available (i.e., page is ready)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configId) {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configId,\n        fetchModelsFromDatabase\n    ]);\n    // Updated: Function to fetch all global custom roles for the authenticated user with caching\n    const fetchUserCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.userCustomRoles) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached custom roles data for: \".concat(configId));\n                setUserCustomRoles(cachedData.userCustomRoles);\n                setIsLoadingUserCustomRoles(false);\n                return;\n            }\n            setIsLoadingUserCustomRoles(true);\n            setUserCustomRolesError(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles\"); // New global endpoint\n                if (!response.ok) {\n                    let errorData;\n                    try {\n                        errorData = await response.json(); // Attempt to parse error as JSON\n                    } catch (e) {\n                        // If error response is not JSON, use text or a generic error\n                        const errorText = await response.text().catch({\n                            \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": ()=>\"HTTP error \".concat(response.status)\n                        }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"]);\n                        errorData = {\n                            error: errorText\n                        };\n                    }\n                    const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : \"Failed to fetch custom roles (status: \".concat(response.status, \")\"));\n                    if (response.status === 401) {\n                        setUserCustomRolesError(errorMessage);\n                    } else {\n                        throw new Error(errorMessage); // Throw for other errors to be caught by the main catch\n                    }\n                    setUserCustomRoles([]); // Clear roles if there was an error handled here\n                } else {\n                    // Only call .json() here if response.ok and body hasn't been read\n                    const data = await response.json();\n                    setUserCustomRoles(data);\n                // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try\n                }\n            } catch (err) {\n                // This catch handles network errors from fetch() or errors thrown from !response.ok block\n                setUserCustomRolesError(err.message);\n                setUserCustomRoles([]); // Clear roles on error\n            } finally{\n                setIsLoadingUserCustomRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"], []);\n    // Fetch API keys and their roles for this config with optimistic loading\n    const fetchKeysAndRolesForConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": async ()=>{\n            if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached keys data for: \".concat(configId));\n                // Process cached keys with roles (same logic as below)\n                const keysWithRolesPromises = cachedData.apiKeys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean);\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: cachedData.defaultChatKeyId === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n                setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);\n                setIsLoadingKeysAndRoles(false);\n                return;\n            }\n            setIsLoadingKeysAndRoles(true);\n            // Preserve config loading errors, clear others\n            setError({\n                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev && prev.startsWith('Error loading model configuration:') ? prev : null\n            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]);\n            setSuccessMessage(null);\n            try {\n                // Fetch all keys for the config\n                const keysResponse = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysResponse.ok) {\n                    const errorData = await keysResponse.json();\n                    throw new Error(errorData.error || 'Failed to fetch API keys');\n                }\n                const keys = await keysResponse.json();\n                // Fetch default general chat key\n                const defaultKeyResponse = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-chat-key\"));\n                if (!defaultKeyResponse.ok) {\n                    console.warn('Failed to fetch default chat key info');\n                }\n                const defaultKeyData = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;\n                setDefaultGeneralChatKeyId((defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) || null);\n                // For each key, fetch its assigned roles\n                const keysWithRolesPromises = keys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    // 1. Check predefined roles\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    // 2. Check current user's global custom roles\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean); // filter(Boolean) removes null entries\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: (defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n            } catch (err) {\n                setError({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev ? \"\".concat(prev, \"; \").concat(err.message) : err.message\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]); // Append if there was a config load error\n            } finally{\n                setIsLoadingKeysAndRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"], [\n        configId,\n        userCustomRoles\n    ]); // Added userCustomRoles to dependency array\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configDetails) {\n                fetchUserCustomRoles(); // Call to fetch custom roles\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        fetchUserCustomRoles\n    ]); // Only depends on configDetails and the stable fetchUserCustomRoles\n    // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Ensure userCustomRoles is not in its initial undefined/null state from useState([])\n            // and actually contains data (or an empty array confirming fetch completion)\n            if (configDetails && userCustomRoles) {\n                fetchKeysAndRolesForConfig();\n            }\n        // This effect runs if configDetails changes, userCustomRoles (state) changes,\n        // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).\n        // This is the desired behavior: re-fetch keys/roles if custom roles change.\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        userCustomRoles,\n        fetchKeysAndRolesForConfig\n    ]);\n    // Updated: Memoize model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === provider\n                }[\"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                // as an OpenRouter key can access any of them.\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    // If for some reason the specific models are not found in fetchedProviderModels,\n                    // it's better to return an empty array or a message than all DeepSeek models unfiltered.\n                    // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.\n                    // For now, strictly showing only these two if found.\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n            }\n            return []; // Return empty array if models haven't been fetched or if fetch failed.\n        }\n    }[\"ConfigDetailsPage.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        provider\n    ]);\n    // Model options for edit modal - filtered by the current key's provider\n    const editModelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[editModelOptions]\": ()=>{\n            if (fetchedProviderModels && editingApiKey) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\": (p)=>p.id === editingApiKey.provider\n                }[\"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"], [\n        fetchedProviderModels,\n        editingApiKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Auto-select the first model from the dynamic modelOptions when provider changes or models load\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            } else {\n                setPredefinedModelId(''); // Clear if no models for provider\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        modelOptions,\n        provider\n    ]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider\n    // Fetch models based on the provider's slug (p.id)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (provider) {\n                // Logic to fetch models for the selected provider slug might need adjustment\n                // if it was previously relying on the provider display name.\n                // Assuming fetchProviderModels is adapted or already uses slugs.\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        provider,\n        fetchModelsFromDatabase\n    ]);\n    const handleSaveKey = async (e)=>{\n        e.preventDefault();\n        if (!configId) {\n            setError('Configuration ID is missing.');\n            return;\n        }\n        // Frontend validation: Check for duplicate models\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.predefined_model_id === predefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');\n            return;\n        }\n        setIsSavingKey(true);\n        setError(null);\n        setSuccessMessage(null);\n        // provider state variable already holds the slug\n        const newKeyData = {\n            custom_api_config_id: configId,\n            provider,\n            predefined_model_id: predefinedModelId,\n            api_key_raw: apiKeyRaw,\n            label,\n            temperature\n        };\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        try {\n            var _PROVIDER_OPTIONS_;\n            const response = await fetch('/api/keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newKeyData)\n            });\n            const result = await response.json();\n            if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');\n            // Create optimistic key object with the returned data\n            const newKey = {\n                id: result.id,\n                custom_api_config_id: configId,\n                provider,\n                predefined_model_id: predefinedModelId,\n                label,\n                temperature,\n                status: 'active',\n                created_at: new Date().toISOString(),\n                last_used_at: null,\n                is_default_general_chat_model: false,\n                assigned_roles: []\n            };\n            // Optimistically add the new key to the list\n            setSavedKeysWithRoles((prevKeys)=>[\n                    ...prevKeys,\n                    newKey\n                ]);\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(label, '\" saved successfully!'));\n            setProvider(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai');\n            setApiKeyRaw('');\n            setLabel('');\n            setTemperature(1.0);\n            // Reset model selection to first available option\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            }\n        } catch (err) {\n            // Revert UI on error\n            setSavedKeysWithRoles(previousKeysState);\n            setError(\"Save Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingKey(false);\n        }\n    };\n    const handleEditKey = (key)=>{\n        setEditingApiKey(key);\n        setEditTemperature(key.temperature || 1.0);\n        setEditPredefinedModelId(key.predefined_model_id);\n    };\n    const handleSaveEdit = async ()=>{\n        if (!editingApiKey) return;\n        // Frontend validation: Check for duplicate models (excluding the current key being edited)\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration.');\n            return;\n        }\n        setIsSavingEdit(true);\n        setError(null);\n        setSuccessMessage(null);\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === editingApiKey.id) {\n                    return {\n                        ...key,\n                        temperature: editTemperature,\n                        predefined_model_id: editPredefinedModelId\n                    };\n                }\n                return key;\n            }));\n        try {\n            const response = await fetch(\"/api/keys?id=\".concat(editingApiKey.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    temperature: editTemperature,\n                    predefined_model_id: editPredefinedModelId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                throw new Error(result.details || result.error || 'Failed to update API key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(editingApiKey.label, '\" updated successfully!'));\n            setEditingApiKey(null);\n        } catch (err) {\n            setError(\"Update Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingEdit(false);\n        }\n    };\n    const handleDeleteKey = (keyId, keyLabel)=>{\n        confirmation.showConfirmation({\n            title: 'Delete API Key',\n            message: 'Are you sure you want to delete the API key \"'.concat(keyLabel, '\"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),\n            confirmText: 'Delete API Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setIsDeletingKey(keyId);\n            setError(null);\n            setSuccessMessage(null);\n            // Store previous state for rollback on error\n            const previousKeysState = [\n                ...savedKeysWithRoles\n            ];\n            const previousDefaultKeyId = defaultGeneralChatKeyId;\n            const keyToDelete = savedKeysWithRoles.find((key)=>key.id === keyId);\n            // Optimistic UI update - immediately remove the key from the list\n            setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n            // If the deleted key was the default, clear the default\n            if (keyToDelete === null || keyToDelete === void 0 ? void 0 : keyToDelete.is_default_general_chat_model) {\n                setDefaultGeneralChatKeyId(null);\n            }\n            try {\n                const response = await fetch(\"/api/keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (!response.ok) {\n                    // Revert UI on error\n                    setSavedKeysWithRoles(previousKeysState);\n                    setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                    // Special handling for 404 errors (key already deleted)\n                    if (response.status === 404) {\n                        // Key was already deleted, so the optimistic update was correct\n                        // Don't revert the UI, just show a different message\n                        setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n                        setSuccessMessage('API key \"'.concat(keyLabel, '\" was already deleted.'));\n                        return; // Don't throw error\n                    }\n                    throw new Error(result.details || result.error || 'Failed to delete API key');\n                }\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage('API key \"'.concat(keyLabel, '\" deleted successfully!'));\n            } catch (err) {\n                setError(\"Delete Key Error: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setIsDeletingKey(null);\n            }\n        });\n    };\n    const handleSetDefaultChatKey = async (apiKeyIdToSet)=>{\n        if (!configId) return;\n        setError(null);\n        setSuccessMessage(null);\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ]; // Keep a copy in case of error\n        const previousDefaultKeyId = defaultGeneralChatKeyId;\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>({\n                    ...key,\n                    is_default_general_chat_model: key.id === apiKeyIdToSet\n                })));\n        setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-key-handler/\").concat(apiKeyIdToSet), {\n                method: 'PUT'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState.map((k)=>({\n                        ...k\n                    }))); // Ensure deep copy for re-render\n                setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                throw new Error(result.details || result.error || 'Failed to set default chat key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage(result.message || 'Default general chat key updated!');\n        } catch (err) {\n            setError(\"Set Default Error: \".concat(err.message));\n        }\n    };\n    const handleRoleToggle = async (apiKey, roleId, isAssigned)=>{\n        setError(null);\n        setSuccessMessage(null);\n        const endpoint = \"/api/keys/\".concat(apiKey.id, \"/roles\");\n        // For optimistic update, find role details from combined list (predefined or user's global custom roles)\n        const allAvailableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id\n                }))\n        ];\n        const roleDetails = allAvailableRoles.find((r)=>r.id === roleId) || {\n            id: roleId,\n            name: roleId,\n            description: ''\n        };\n        const previousKeysState = savedKeysWithRoles.map((k)=>({\n                ...k,\n                assigned_roles: [\n                    ...k.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            })); // Deep copy\n        let previousEditingRolesApiKey = null;\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            previousEditingRolesApiKey = {\n                ...editingRolesApiKey,\n                assigned_roles: [\n                    ...editingRolesApiKey.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            };\n        }\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === apiKey.id) {\n                    const updatedRoles = isAssigned ? key.assigned_roles.filter((r)=>r.id !== roleId) : [\n                        ...key.assigned_roles,\n                        roleDetails\n                    ];\n                    return {\n                        ...key,\n                        assigned_roles: updatedRoles\n                    };\n                }\n                return key;\n            }));\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            setEditingRolesApiKey((prevEditingKey)=>{\n                if (!prevEditingKey) return null;\n                const updatedRoles = isAssigned ? prevEditingKey.assigned_roles.filter((r)=>r.id !== roleId) : [\n                    ...prevEditingKey.assigned_roles,\n                    roleDetails\n                ];\n                return {\n                    ...prevEditingKey,\n                    assigned_roles: updatedRoles\n                };\n            });\n        }\n        try {\n            let response;\n            if (isAssigned) {\n                response = await fetch(\"\".concat(endpoint, \"/\").concat(roleId), {\n                    method: 'DELETE'\n                });\n            } else {\n                response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        role_name: roleId\n                    })\n                });\n            }\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                if (previousEditingRolesApiKey) {\n                    setEditingRolesApiKey(previousEditingRolesApiKey);\n                } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n                    const originalKeyData = previousKeysState.find((k)=>k.id === apiKey.id);\n                    if (originalKeyData) setEditingRolesApiKey(originalKeyData);\n                }\n                // Use the error message from the backend if available (e.g., for 409 conflict)\n                const errorMessage = response.status === 409 && result.error ? result.error : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');\n                throw new Error(errorMessage);\n            }\n            setSuccessMessage(result.message || \"Role '\".concat(roleDetails.name, \"' \").concat(isAssigned ? 'unassigned' : 'assigned', \" successfully.\"));\n        } catch (err) {\n            // err.message now contains the potentially more user-friendly message from the backend or a fallback\n            setError(\"Role Update Error: \".concat(err.message));\n        }\n    };\n    const handleCreateCustomRole = async ()=>{\n        // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.\n        // configId is also not needed for creating a global role.\n        if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {\n            setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');\n            return;\n        }\n        // Check against PREDEFINED_ROLES and the user's existing global custom roles\n        if (_config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.some((pr)=>pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || userCustomRoles.some((cr)=>cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {\n            setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');\n            return;\n        }\n        if (!newCustomRoleName.trim()) {\n            setCreateCustomRoleError('Role Name is required.');\n            return;\n        }\n        setCreateCustomRoleError(null);\n        setIsSavingCustomRole(true);\n        try {\n            const response = await fetch(\"/api/user/custom-roles\", {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    role_id: newCustomRoleId.trim(),\n                    name: newCustomRoleName.trim(),\n                    description: newCustomRoleDescription.trim()\n                })\n            });\n            if (!response.ok) {\n                // Try to parse the error response as JSON, but fallback if it's not JSON\n                let errorResult;\n                try {\n                    errorResult = await response.json();\n                } catch (parseError) {\n                    // If JSON parsing fails, use the response text or a generic status message\n                    const errorText = await response.text().catch(()=>\"HTTP status \".concat(response.status));\n                    errorResult = {\n                        error: \"Server error, could not parse response.\",\n                        details: errorText\n                    };\n                }\n                let displayError = errorResult.error || 'Failed to create custom role.';\n                if (errorResult.details) {\n                    displayError += \" (Details: \".concat(errorResult.details, \")\");\n                } else if (errorResult.issues) {\n                    // If Zod issues, format them for better readability\n                    const issuesString = Object.entries(errorResult.issues).map((param)=>{\n                        let [field, messages] = param;\n                        return \"\".concat(field, \": \").concat(messages.join(', '));\n                    }).join('; ');\n                    displayError += \" (Issues: \".concat(issuesString, \")\");\n                }\n                throw new Error(displayError);\n            }\n            // If response IS ok, then parse the successful JSON response\n            const result = await response.json();\n            setNewCustomRoleId('');\n            setNewCustomRoleName('');\n            setNewCustomRoleDescription('');\n            // setShowCreateCustomRoleForm(false); // User might want to add multiple roles\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            // Add the new role optimistically to the local state\n            const newRole = {\n                id: result.id,\n                role_id: result.role_id,\n                name: result.name,\n                description: result.description,\n                user_id: result.user_id,\n                created_at: result.created_at,\n                updated_at: result.updated_at\n            };\n            setUserCustomRoles((prev)=>[\n                    ...prev,\n                    newRole\n                ]);\n            setSuccessMessage(\"Custom role '\".concat(result.name, \"' created successfully! It is now available globally.\"));\n        } catch (err) {\n            setCreateCustomRoleError(err.message);\n        } finally{\n            setIsSavingCustomRole(false);\n        }\n    };\n    const handleDeleteCustomRole = (customRoleDatabaseId, customRoleName)=>{\n        // configId is not needed for deleting a global role\n        if (!customRoleDatabaseId) return;\n        confirmation.showConfirmation({\n            title: 'Delete Custom Role',\n            message: 'Are you sure you want to delete the custom role \"'.concat(customRoleName, \"\\\"? This will unassign it from all API keys where it's currently used. This action cannot be undone.\"),\n            confirmText: 'Delete Role',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setDeletingCustomRoleId(customRoleDatabaseId);\n            setUserCustomRolesError(null);\n            setCreateCustomRoleError(null);\n            setSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles/\".concat(customRoleDatabaseId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json(); // Try to parse JSON for all responses\n                if (!response.ok) {\n                    throw new Error(result.error || 'Failed to delete custom role');\n                }\n                // Optimistically remove from the local state\n                setUserCustomRoles((prev)=>prev.filter((role)=>role.id !== customRoleDatabaseId));\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage(result.message || 'Global custom role \"'.concat(customRoleName, '\" deleted successfully.'));\n                // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.\n                // This ensures the displayed assigned roles for keys on this page are up-to-date.\n                if (configId) {\n                    fetchKeysAndRolesForConfig();\n                }\n            } catch (err) {\n                setUserCustomRolesError(\"Error deleting role: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setDeletingCustomRoleId(null);\n            }\n        });\n    };\n    const renderManageRolesModal = ()=>{\n        if (!editingRolesApiKey) return null;\n        const combinedRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id // The actual DB ID (UUID) for delete operations\n                }))\n        ].sort((a, b)=>a.name.localeCompare(b.name));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: [\n                                    \"Manage Roles for: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-400\",\n                                        children: editingRolesApiKey.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1000,\n                                        columnNumber: 80\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1000,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1001,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 999,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            userCustomRolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 text-sm\",\n                                    children: [\n                                        \"Error with custom roles: \",\n                                        userCustomRolesError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                feature: \"custom_roles\",\n                                customMessage: \"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCreateCustomRoleForm(!showCreateCustomRoleForm),\n                                            className: \"btn-primary text-sm inline-flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1022,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1017,\n                                        columnNumber: 15\n                                    }, this),\n                                    showCreateCustomRoleForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-md font-medium text-white mb-3\",\n                                                children: \"Create New Custom Role for this Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1029,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleId\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Role ID (short, no spaces, max 30 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleId\",\n                                                                value: newCustomRoleId,\n                                                                onChange: (e)=>setNewCustomRoleId(e.target.value.replace(/\\s/g, '')),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 30,\n                                                                placeholder: \"e.g., my_blog_writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1033,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleName\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Display Name (max 100 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1042,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleName\",\n                                                                value: newCustomRoleName,\n                                                                onChange: (e)=>setNewCustomRoleName(e.target.value),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 100,\n                                                                placeholder: \"e.g., My Awesome Blog Writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1043,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleDescription\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Description (optional, max 500 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"newCustomRoleDescription\",\n                                                                value: newCustomRoleDescription,\n                                                                onChange: (e)=>setNewCustomRoleDescription(e.target.value),\n                                                                rows: 2,\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 500,\n                                                                placeholder: \"Optional: Describe what this role is for...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    createCustomRoleError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-900/50 border border-red-800/50 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 text-sm\",\n                                                            children: createCustomRoleError\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateCustomRole,\n                                                        disabled: isSavingCustomRole,\n                                                        className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1013,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1006,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-300 mb-3\",\n                                children: \"Select roles to assign:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1081,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-y-auto space-y-2\",\n                                style: {\n                                    maxHeight: 'calc(90vh - 350px)'\n                                },\n                                children: [\n                                    isLoadingUserCustomRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm ml-2\",\n                                                children: \"Loading custom roles...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1084,\n                                        columnNumber: 17\n                                    }, this),\n                                    combinedRoles.map((role)=>{\n                                        const isAssigned = editingRolesApiKey.assigned_roles.some((ar)=>ar.id === role.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 \".concat(isAssigned ? 'bg-orange-500/20 border-orange-500/30 shadow-sm' : 'bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"role-\".concat(role.id),\n                                                    className: \"flex items-center cursor-pointer flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"role-\".concat(role.id),\n                                                            checked: isAssigned,\n                                                            onChange: ()=>handleRoleToggle(editingRolesApiKey, role.id, isAssigned),\n                                                            className: \"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-sm font-medium \".concat(isAssigned ? 'text-orange-300' : 'text-white'),\n                                                            children: role.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1105,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        role.isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300\",\n                                                            children: \"Custom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1109,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1097,\n                                                    columnNumber: 21\n                                                }, this),\n                                                role.isCustom && role.databaseId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteCustomRole(role.databaseId, role.name),\n                                                    disabled: deletingCustomRoleId === role.databaseId,\n                                                    className: \"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2\",\n                                                    title: \"Delete this custom role\",\n                                                    children: deletingCustomRoleId === role.databaseId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 70\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 123\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, role.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1082,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1080,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"btn-secondary\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1132,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1131,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 998,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 997,\n            columnNumber: 7\n        }, this);\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1147,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoadingConfig && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__.CompactManageKeysLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1151,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: configDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-6 w-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-3xl font-bold text-white\",\n                                                                    children: configDetails.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1178,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400 mt-1\",\n                                                                    children: \"Model Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1181,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1177,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"ID: \",\n                                                        configDetails.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1184,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : error && !isLoadingConfig ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-red-400\",\n                                                            children: \"Configuration Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1195,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 mt-1\",\n                                                            children: error.replace(\"Error loading model configuration: \", \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1196,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1190,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-gray-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"Loading Configuration...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1205,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 mt-1\",\n                                                            children: \"Please wait while we fetch your model details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1206,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1204,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1170,\n                                        columnNumber: 13\n                                    }, this),\n                                    configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateOptimistically(\"/routing-setup/\".concat(configId, \"?from=model-config\")),\n                                            className: \"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group\",\n                                            ...createRoutingHoverPrefetch(configId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Advanced Routing Setup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1218,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1169,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1168,\n                            columnNumber: 11\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1235,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-300 font-medium\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1234,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1233,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1243,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-300 font-medium\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1244,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1242,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1241,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1158,\n                    columnNumber: 9\n                }, this),\n                configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('provider-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'provider-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Provider API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('user-api-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'user-api-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1277,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Generated API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1254,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1253,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'provider-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1292,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1291,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"Add Provider API Key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1295,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Configure new provider key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1296,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1294,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSaveKey,\n                                                className: \"space-y-5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"provider\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Provider\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1303,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"provider\",\n                                                                        value: provider,\n                                                                        onChange: (e)=>{\n                                                                            setProvider(e.target.value);\n                                                                        },\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm\",\n                                                                        children: PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                className: \"bg-gray-800 text-white\",\n                                                                                children: option.label\n                                                                            }, option.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1313,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1306,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"apiKeyRaw\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"API Key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1319,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"apiKeyRaw\",\n                                                                        type: \"password\",\n                                                                        value: apiKeyRaw,\n                                                                        onChange: (e)=>setApiKeyRaw(e.target.value),\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm\",\n                                                                        placeholder: \"Enter your API key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1322,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1333,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Fetching models...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1332,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                                                                        children: fetchProviderModelsError\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1338,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1318,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"predefinedModelId\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Model Variant\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1343,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"predefinedModelId\",\n                                                                        value: predefinedModelId,\n                                                                        onChange: (e)=>setPredefinedModelId(e.target.value),\n                                                                        disabled: !modelOptions.length,\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500\",\n                                                                        children: modelOptions.length > 0 ? modelOptions.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: m.value,\n                                                                                className: \"bg-gray-800 text-white\",\n                                                                                children: m.label\n                                                                            }, m.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1355,\n                                                                                columnNumber: 27\n                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            disabled: true,\n                                                                            className: \"bg-gray-800 text-gray-400\",\n                                                                            children: fetchedProviderModels === null && isFetchingProviderModels ? \"Loading models...\" : \"Select a provider first\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1358,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1346,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"label\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Label\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1364,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"label\",\n                                                                        value: label,\n                                                                        onChange: (e)=>setLabel(e.target.value),\n                                                                        required: true,\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm\",\n                                                                        placeholder: \"e.g., My OpenAI GPT-4o Key #1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1367,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"temperature\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: [\n                                                                            \"Temperature\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-400 ml-1\",\n                                                                                children: \"(0.0 - 2.0)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1381,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1379,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                id: \"temperature\",\n                                                                                min: \"0\",\n                                                                                max: \"2\",\n                                                                                step: \"0.1\",\n                                                                                value: temperature,\n                                                                                onChange: (e)=>setTemperature(parseFloat(e.target.value)),\n                                                                                className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1384,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: \"Conservative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1395,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"number\",\n                                                                                            min: \"0\",\n                                                                                            max: \"2\",\n                                                                                            step: \"0.1\",\n                                                                                            value: temperature,\n                                                                                            onChange: (e)=>setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0))),\n                                                                                            className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1397,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1396,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: \"Creative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1407,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1394,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1409,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1383,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1378,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1301,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim(),\n                                                        className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm\",\n                                                        children: isSavingKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1423,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Saving...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1428,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Add API Key\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1416,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1438,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-blue-300 mb-1\",\n                                                                    children: \"Key Configuration Rules\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1440,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same API key, different models:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1442,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1442,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Different API keys, same model:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1443,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1443,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"❌ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same model twice:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1444,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Not allowed in one configuration\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1444,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1441,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1439,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1437,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1436,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1289,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1288,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1457,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1456,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"API Keys & Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1460,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Manage existing keys\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1461,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1459,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1455,\n                                                columnNumber: 15\n                                            }, this),\n                                            isLoadingKeysAndRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1467,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Loading API keys...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1468,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1466,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || error && error.startsWith(\"Error loading model configuration:\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1475,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-1\",\n                                                        children: \"No API Keys\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Add your first key using the form\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                                children: savedKeysWithRoles.map((key, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in\",\n                                                        style: {\n                                                            animationDelay: \"\".concat(index * 50, \"ms\")\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-sm font-semibold text-white truncate mr-2\",\n                                                                                    children: key.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1489,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1492,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        \"Default\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1491,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1488,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: [\n                                                                                                key.provider,\n                                                                                                \" (\",\n                                                                                                key.predefined_model_id,\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1500,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50\",\n                                                                                            children: [\n                                                                                                \"Temp: \",\n                                                                                                key.temperature\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1503,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1499,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                                    feature: \"custom_roles\",\n                                                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: \"Roles available on Starter plan+\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1512,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1511,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: key.assigned_roles.length > 0 ? key.assigned_roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50\",\n                                                                                                children: role.name\n                                                                                            }, role.id, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                                lineNumber: 1521,\n                                                                                                columnNumber: 37\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: \"No roles\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1526,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1518,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1508,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1498,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        !key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSetDefaultChatKey(key.id),\n                                                                            className: \"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Set as default chat model\",\n                                                                            children: \"Set Default\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1533,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1487,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 ml-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleEditKey(key),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Edit Model & Settings\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1552,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1545,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                            feature: \"custom_roles\",\n                                                                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                disabled: true,\n                                                                                className: \"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Role management requires Starter plan or higher\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1563,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1557,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setEditingRolesApiKey(key),\n                                                                                disabled: isDeletingKey === key.id,\n                                                                                className: \"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Manage Roles\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1574,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1567,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1554,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteKey(key.id, key.label),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Delete Key\",\n                                                                            children: isDeletingKey === key.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1585,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1587,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1577,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1544,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, key.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1485,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1483,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && error && !error.startsWith(\"Error loading model configuration:\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1600,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 font-medium text-sm\",\n                                                            children: [\n                                                                \"Could not load API keys/roles: \",\n                                                                error\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1599,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1598,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1454,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1453,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1286,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'user-api-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__.ApiKeyManager, {\n                                configId: configId,\n                                configName: configDetails.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1613,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1612,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1251,\n                    columnNumber: 9\n                }, this),\n                editingRolesApiKey && renderManageRolesModal(),\n                editingApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Edit API Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1630,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingApiKey(null),\n                                        className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1635,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1631,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1629,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-2\",\n                                                children: editingApiKey.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1641,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"Current: \",\n                                                    editingApiKey.provider,\n                                                    \" (\",\n                                                    editingApiKey.predefined_model_id,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1642,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1640,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Provider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1649,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300\",\n                                                        children: ((_llmProviders_find = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find((p)=>p.id === editingApiKey.provider)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.name) || editingApiKey.provider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1652,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: \"Provider cannot be changed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1655,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1648,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editModelId\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1659,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"editModelId\",\n                                                        value: editPredefinedModelId,\n                                                        onChange: (e)=>setEditPredefinedModelId(e.target.value),\n                                                        disabled: !editModelOptions.length,\n                                                        className: \"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30\",\n                                                        children: editModelOptions.length > 0 ? editModelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1671,\n                                                                columnNumber: 25\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            disabled: true,\n                                                            children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1676,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1662,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1658,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editTemperature\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: [\n                                                            \"Temperature: \",\n                                                            editTemperature\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1684,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        id: \"editTemperature\",\n                                                        min: \"0\",\n                                                        max: \"2\",\n                                                        step: \"0.1\",\n                                                        value: editTemperature,\n                                                        onChange: (e)=>setEditTemperature(parseFloat(e.target.value)),\n                                                        className: \"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1687,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"0.0 (Focused)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1698,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"1.0 (Balanced)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1699,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"2.0 (Creative)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1700,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1697,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1683,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1705,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1704,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1647,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1639,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setEditingApiKey(null),\n                                            className: \"btn-secondary\",\n                                            disabled: isSavingEdit,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1714,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSaveEdit,\n                                            disabled: isSavingEdit,\n                                            className: \"btn-primary\",\n                                            children: isSavingEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1728,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Saving...\"\n                                                ]\n                                            }, void 0, true) : 'Save Changes'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1721,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1713,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1712,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1628,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1627,\n                    columnNumber: 9\n                }, this),\n                !configDetails && !isLoadingConfig && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"h-8 w-8 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1744,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1743,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                            children: \"Model Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1746,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-8\",\n                            children: \"This API Model configuration could not be found or may have been deleted.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1747,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1752,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1748,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1742,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1759,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                    id: \"global-tooltip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1771,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1156,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1155,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigDetailsPage, \"iTFE/LOBiBHfFvkWnhfyoHl4n4c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe,\n        _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = ConfigDetailsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"ConfigDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/my-models/[configId]/page.tsx\n"));

/***/ })

});