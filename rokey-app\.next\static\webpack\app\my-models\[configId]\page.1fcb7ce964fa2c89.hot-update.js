"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/my-models/[configId]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/my-models/[configId]/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfigDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useManageKeysPrefetch */ \"(app-pages-browser)/./src/hooks/useManageKeysPrefetch.ts\");\n/* harmony import */ var _components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ManageKeysLoadingSkeleton */ \"(app-pages-browser)/./src/components/ManageKeysLoadingSkeleton.tsx\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/UserApiKeys/ApiKeyManager */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n // For accessing route params\n // Ensure path is correct\n\n\n\n\n\n\n\n\n\n\n\n// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction ConfigDetailsPage() {\n    var _PROVIDER_OPTIONS_, _llmProviders_find;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const configId = params.configId;\n    // Confirmation modal hook\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation)();\n    // Navigation hook with safe context\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe)();\n    const navigateOptimistically = (navigationContext === null || navigationContext === void 0 ? void 0 : navigationContext.navigateOptimistically) || ((href)=>{\n        window.location.href = href;\n    });\n    // Prefetch hooks\n    const { getCachedData, isCached, clearCache } = (0,_hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch)();\n    const { createHoverPrefetch: createRoutingHoverPrefetch } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch)();\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingConfig, setIsLoadingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Provider state now stores the slug (p.id)\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai'); // Stores slug\n    const [predefinedModelId, setPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [apiKeyRaw, setApiKeyRaw] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [label, setLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [temperature, setTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [isSavingKey, setIsSavingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for dynamic model fetching\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [savedKeysWithRoles, setSavedKeysWithRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeletingKey, setIsDeletingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingRolesApiKey, setEditingRolesApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for editing API keys\n    const [editingApiKey, setEditingApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTemperature, setEditTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [editPredefinedModelId, setEditPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingEdit, setIsSavingEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for User-Defined Custom Roles\n    const [userCustomRoles, setUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCustomRolesError, setUserCustomRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newCustomRoleId, setNewCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleName, setNewCustomRoleName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleDescription, setNewCustomRoleDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingCustomRole, setIsSavingCustomRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createCustomRoleError, setCreateCustomRoleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCustomRoleId, setDeletingCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // stores the DB ID (UUID) of the custom role\n    // State for tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('provider-keys');\n    // Fetch config details with optimistic loading\n    const fetchConfigDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchConfigDetails]\": async ()=>{\n            if (!configId) return;\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached config data for: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                setIsLoadingConfig(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoadingConfig(true);\n            setError(null);\n            try {\n                const res = await fetch(\"/api/custom-configs\");\n                if (!res.ok) {\n                    const errData = await res.json();\n                    throw new Error(errData.error || 'Failed to fetch configurations list');\n                }\n                const allConfigs = await res.json();\n                const currentConfig = allConfigs.find({\n                    \"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\": (c)=>c.id === configId\n                }[\"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\"]);\n                if (!currentConfig) throw new Error('Configuration not found in the list.');\n                setConfigDetails(currentConfig);\n            } catch (err) {\n                setError(\"Error loading model configuration: \".concat(err.message));\n                setConfigDetails(null);\n            } finally{\n                setIsLoadingConfig(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchConfigDetails]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            fetchConfigDetails();\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        fetchConfigDetails\n    ]);\n    // New: Function to fetch all models from the database with caching\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.models) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached models data for: \".concat(configId));\n                setFetchedProviderModels(cachedData.models);\n                setIsFetchingProviderModels(false);\n                return;\n            }\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                // The new API doesn't need a specific provider or API key in the body to list models\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                setFetchProviderModelsError(\"Error fetching models: \".concat(err.message));\n                setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\"], [\n        configId,\n        getCachedData\n    ]);\n    // New: Fetch all models from DB when configId is available (i.e., page is ready)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configId) {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configId,\n        fetchModelsFromDatabase\n    ]);\n    // Updated: Function to fetch all global custom roles for the authenticated user with caching\n    const fetchUserCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.userCustomRoles) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached custom roles data for: \".concat(configId));\n                setUserCustomRoles(cachedData.userCustomRoles);\n                setIsLoadingUserCustomRoles(false);\n                return;\n            }\n            setIsLoadingUserCustomRoles(true);\n            setUserCustomRolesError(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles\"); // New global endpoint\n                if (!response.ok) {\n                    let errorData;\n                    try {\n                        errorData = await response.json(); // Attempt to parse error as JSON\n                    } catch (e) {\n                        // If error response is not JSON, use text or a generic error\n                        const errorText = await response.text().catch({\n                            \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": ()=>\"HTTP error \".concat(response.status)\n                        }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"]);\n                        errorData = {\n                            error: errorText\n                        };\n                    }\n                    const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : \"Failed to fetch custom roles (status: \".concat(response.status, \")\"));\n                    if (response.status === 401) {\n                        setUserCustomRolesError(errorMessage);\n                    } else {\n                        throw new Error(errorMessage); // Throw for other errors to be caught by the main catch\n                    }\n                    setUserCustomRoles([]); // Clear roles if there was an error handled here\n                } else {\n                    // Only call .json() here if response.ok and body hasn't been read\n                    const data = await response.json();\n                    setUserCustomRoles(data);\n                // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try\n                }\n            } catch (err) {\n                // This catch handles network errors from fetch() or errors thrown from !response.ok block\n                setUserCustomRolesError(err.message);\n                setUserCustomRoles([]); // Clear roles on error\n            } finally{\n                setIsLoadingUserCustomRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"], []);\n    // Fetch API keys and their roles for this config with optimistic loading\n    const fetchKeysAndRolesForConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": async ()=>{\n            if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached keys data for: \".concat(configId));\n                // Process cached keys with roles (same logic as below)\n                const keysWithRolesPromises = cachedData.apiKeys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean);\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: cachedData.defaultChatKeyId === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n                setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);\n                setIsLoadingKeysAndRoles(false);\n                return;\n            }\n            setIsLoadingKeysAndRoles(true);\n            // Preserve config loading errors, clear others\n            setError({\n                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev && prev.startsWith('Error loading model configuration:') ? prev : null\n            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]);\n            setSuccessMessage(null);\n            try {\n                // Fetch all keys for the config\n                const keysResponse = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysResponse.ok) {\n                    const errorData = await keysResponse.json();\n                    throw new Error(errorData.error || 'Failed to fetch API keys');\n                }\n                const keys = await keysResponse.json();\n                // Fetch default general chat key\n                const defaultKeyResponse = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-chat-key\"));\n                if (!defaultKeyResponse.ok) {\n                    console.warn('Failed to fetch default chat key info');\n                }\n                const defaultKeyData = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;\n                setDefaultGeneralChatKeyId((defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) || null);\n                // For each key, fetch its assigned roles\n                const keysWithRolesPromises = keys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    // 1. Check predefined roles\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    // 2. Check current user's global custom roles\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean); // filter(Boolean) removes null entries\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: (defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n            } catch (err) {\n                setError({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev ? \"\".concat(prev, \"; \").concat(err.message) : err.message\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]); // Append if there was a config load error\n            } finally{\n                setIsLoadingKeysAndRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"], [\n        configId,\n        userCustomRoles\n    ]); // Added userCustomRoles to dependency array\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configDetails) {\n                fetchUserCustomRoles(); // Call to fetch custom roles\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        fetchUserCustomRoles\n    ]); // Only depends on configDetails and the stable fetchUserCustomRoles\n    // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Ensure userCustomRoles is not in its initial undefined/null state from useState([])\n            // and actually contains data (or an empty array confirming fetch completion)\n            if (configDetails && userCustomRoles) {\n                fetchKeysAndRolesForConfig();\n            }\n        // This effect runs if configDetails changes, userCustomRoles (state) changes,\n        // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).\n        // This is the desired behavior: re-fetch keys/roles if custom roles change.\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        userCustomRoles,\n        fetchKeysAndRolesForConfig\n    ]);\n    // Updated: Memoize model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === provider\n                }[\"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                // as an OpenRouter key can access any of them.\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    // If for some reason the specific models are not found in fetchedProviderModels,\n                    // it's better to return an empty array or a message than all DeepSeek models unfiltered.\n                    // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.\n                    // For now, strictly showing only these two if found.\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n            }\n            return []; // Return empty array if models haven't been fetched or if fetch failed.\n        }\n    }[\"ConfigDetailsPage.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        provider\n    ]);\n    // Model options for edit modal - filtered by the current key's provider\n    const editModelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[editModelOptions]\": ()=>{\n            if (fetchedProviderModels && editingApiKey) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\": (p)=>p.id === editingApiKey.provider\n                }[\"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"], [\n        fetchedProviderModels,\n        editingApiKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Auto-select the first model from the dynamic modelOptions when provider changes or models load\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            } else {\n                setPredefinedModelId(''); // Clear if no models for provider\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        modelOptions,\n        provider\n    ]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider\n    // Fetch models based on the provider's slug (p.id)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (provider) {\n                // Logic to fetch models for the selected provider slug might need adjustment\n                // if it was previously relying on the provider display name.\n                // Assuming fetchProviderModels is adapted or already uses slugs.\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        provider,\n        fetchModelsFromDatabase\n    ]);\n    const handleSaveKey = async (e)=>{\n        e.preventDefault();\n        if (!configId) {\n            setError('Configuration ID is missing.');\n            return;\n        }\n        // Frontend validation: Check for duplicate models\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.predefined_model_id === predefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');\n            return;\n        }\n        setIsSavingKey(true);\n        setError(null);\n        setSuccessMessage(null);\n        // provider state variable already holds the slug\n        const newKeyData = {\n            custom_api_config_id: configId,\n            provider,\n            predefined_model_id: predefinedModelId,\n            api_key_raw: apiKeyRaw,\n            label,\n            temperature\n        };\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        try {\n            var _PROVIDER_OPTIONS_;\n            const response = await fetch('/api/keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newKeyData)\n            });\n            const result = await response.json();\n            if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');\n            // Create optimistic key object with the returned data\n            const newKey = {\n                id: result.id,\n                custom_api_config_id: configId,\n                provider,\n                predefined_model_id: predefinedModelId,\n                label,\n                temperature,\n                status: 'active',\n                created_at: new Date().toISOString(),\n                last_used_at: null,\n                is_default_general_chat_model: false,\n                assigned_roles: []\n            };\n            // Optimistically add the new key to the list\n            setSavedKeysWithRoles((prevKeys)=>[\n                    ...prevKeys,\n                    newKey\n                ]);\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(label, '\" saved successfully!'));\n            setProvider(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai');\n            setApiKeyRaw('');\n            setLabel('');\n            setTemperature(1.0);\n            // Reset model selection to first available option\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            }\n        } catch (err) {\n            // Revert UI on error\n            setSavedKeysWithRoles(previousKeysState);\n            setError(\"Save Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingKey(false);\n        }\n    };\n    const handleEditKey = (key)=>{\n        setEditingApiKey(key);\n        setEditTemperature(key.temperature || 1.0);\n        setEditPredefinedModelId(key.predefined_model_id);\n    };\n    const handleSaveEdit = async ()=>{\n        if (!editingApiKey) return;\n        // Frontend validation: Check for duplicate models (excluding the current key being edited)\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration.');\n            return;\n        }\n        setIsSavingEdit(true);\n        setError(null);\n        setSuccessMessage(null);\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === editingApiKey.id) {\n                    return {\n                        ...key,\n                        temperature: editTemperature,\n                        predefined_model_id: editPredefinedModelId\n                    };\n                }\n                return key;\n            }));\n        try {\n            const response = await fetch(\"/api/keys?id=\".concat(editingApiKey.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    temperature: editTemperature,\n                    predefined_model_id: editPredefinedModelId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                throw new Error(result.details || result.error || 'Failed to update API key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(editingApiKey.label, '\" updated successfully!'));\n            setEditingApiKey(null);\n        } catch (err) {\n            setError(\"Update Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingEdit(false);\n        }\n    };\n    const handleDeleteKey = (keyId, keyLabel)=>{\n        confirmation.showConfirmation({\n            title: 'Delete API Key',\n            message: 'Are you sure you want to delete the API key \"'.concat(keyLabel, '\"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),\n            confirmText: 'Delete API Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setIsDeletingKey(keyId);\n            setError(null);\n            setSuccessMessage(null);\n            // Store previous state for rollback on error\n            const previousKeysState = [\n                ...savedKeysWithRoles\n            ];\n            const previousDefaultKeyId = defaultGeneralChatKeyId;\n            const keyToDelete = savedKeysWithRoles.find((key)=>key.id === keyId);\n            // Optimistic UI update - immediately remove the key from the list\n            setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n            // If the deleted key was the default, clear the default\n            if (keyToDelete === null || keyToDelete === void 0 ? void 0 : keyToDelete.is_default_general_chat_model) {\n                setDefaultGeneralChatKeyId(null);\n            }\n            try {\n                const response = await fetch(\"/api/keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (!response.ok) {\n                    // Revert UI on error\n                    setSavedKeysWithRoles(previousKeysState);\n                    setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                    // Special handling for 404 errors (key already deleted)\n                    if (response.status === 404) {\n                        // Key was already deleted, so the optimistic update was correct\n                        // Don't revert the UI, just show a different message\n                        setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n                        setSuccessMessage('API key \"'.concat(keyLabel, '\" was already deleted.'));\n                        return; // Don't throw error\n                    }\n                    throw new Error(result.details || result.error || 'Failed to delete API key');\n                }\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage('API key \"'.concat(keyLabel, '\" deleted successfully!'));\n            } catch (err) {\n                setError(\"Delete Key Error: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setIsDeletingKey(null);\n            }\n        });\n    };\n    const handleSetDefaultChatKey = async (apiKeyIdToSet)=>{\n        if (!configId) return;\n        setError(null);\n        setSuccessMessage(null);\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ]; // Keep a copy in case of error\n        const previousDefaultKeyId = defaultGeneralChatKeyId;\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>({\n                    ...key,\n                    is_default_general_chat_model: key.id === apiKeyIdToSet\n                })));\n        setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-key-handler/\").concat(apiKeyIdToSet), {\n                method: 'PUT'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState.map((k)=>({\n                        ...k\n                    }))); // Ensure deep copy for re-render\n                setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                throw new Error(result.details || result.error || 'Failed to set default chat key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage(result.message || 'Default general chat key updated!');\n        } catch (err) {\n            setError(\"Set Default Error: \".concat(err.message));\n        }\n    };\n    const handleRoleToggle = async (apiKey, roleId, isAssigned)=>{\n        setError(null);\n        setSuccessMessage(null);\n        const endpoint = \"/api/keys/\".concat(apiKey.id, \"/roles\");\n        // For optimistic update, find role details from combined list (predefined or user's global custom roles)\n        const allAvailableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id\n                }))\n        ];\n        const roleDetails = allAvailableRoles.find((r)=>r.id === roleId) || {\n            id: roleId,\n            name: roleId,\n            description: ''\n        };\n        const previousKeysState = savedKeysWithRoles.map((k)=>({\n                ...k,\n                assigned_roles: [\n                    ...k.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            })); // Deep copy\n        let previousEditingRolesApiKey = null;\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            previousEditingRolesApiKey = {\n                ...editingRolesApiKey,\n                assigned_roles: [\n                    ...editingRolesApiKey.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            };\n        }\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === apiKey.id) {\n                    const updatedRoles = isAssigned ? key.assigned_roles.filter((r)=>r.id !== roleId) : [\n                        ...key.assigned_roles,\n                        roleDetails\n                    ];\n                    return {\n                        ...key,\n                        assigned_roles: updatedRoles\n                    };\n                }\n                return key;\n            }));\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            setEditingRolesApiKey((prevEditingKey)=>{\n                if (!prevEditingKey) return null;\n                const updatedRoles = isAssigned ? prevEditingKey.assigned_roles.filter((r)=>r.id !== roleId) : [\n                    ...prevEditingKey.assigned_roles,\n                    roleDetails\n                ];\n                return {\n                    ...prevEditingKey,\n                    assigned_roles: updatedRoles\n                };\n            });\n        }\n        try {\n            let response;\n            if (isAssigned) {\n                response = await fetch(\"\".concat(endpoint, \"/\").concat(roleId), {\n                    method: 'DELETE'\n                });\n            } else {\n                response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        role_name: roleId\n                    })\n                });\n            }\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                if (previousEditingRolesApiKey) {\n                    setEditingRolesApiKey(previousEditingRolesApiKey);\n                } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n                    const originalKeyData = previousKeysState.find((k)=>k.id === apiKey.id);\n                    if (originalKeyData) setEditingRolesApiKey(originalKeyData);\n                }\n                // Use the error message from the backend if available (e.g., for 409 conflict)\n                const errorMessage = response.status === 409 && result.error ? result.error : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');\n                throw new Error(errorMessage);\n            }\n            setSuccessMessage(result.message || \"Role '\".concat(roleDetails.name, \"' \").concat(isAssigned ? 'unassigned' : 'assigned', \" successfully.\"));\n        } catch (err) {\n            // err.message now contains the potentially more user-friendly message from the backend or a fallback\n            setError(\"Role Update Error: \".concat(err.message));\n        }\n    };\n    const handleCreateCustomRole = async ()=>{\n        // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.\n        // configId is also not needed for creating a global role.\n        if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {\n            setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');\n            return;\n        }\n        // Check against PREDEFINED_ROLES and the user's existing global custom roles\n        if (_config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.some((pr)=>pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || userCustomRoles.some((cr)=>cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {\n            setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');\n            return;\n        }\n        if (!newCustomRoleName.trim()) {\n            setCreateCustomRoleError('Role Name is required.');\n            return;\n        }\n        setCreateCustomRoleError(null);\n        setIsSavingCustomRole(true);\n        try {\n            const response = await fetch(\"/api/user/custom-roles\", {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    role_id: newCustomRoleId.trim(),\n                    name: newCustomRoleName.trim(),\n                    description: newCustomRoleDescription.trim()\n                })\n            });\n            if (!response.ok) {\n                // Try to parse the error response as JSON, but fallback if it's not JSON\n                let errorResult;\n                try {\n                    errorResult = await response.json();\n                } catch (parseError) {\n                    // If JSON parsing fails, use the response text or a generic status message\n                    const errorText = await response.text().catch(()=>\"HTTP status \".concat(response.status));\n                    errorResult = {\n                        error: \"Server error, could not parse response.\",\n                        details: errorText\n                    };\n                }\n                let displayError = errorResult.error || 'Failed to create custom role.';\n                if (errorResult.details) {\n                    displayError += \" (Details: \".concat(errorResult.details, \")\");\n                } else if (errorResult.issues) {\n                    // If Zod issues, format them for better readability\n                    const issuesString = Object.entries(errorResult.issues).map((param)=>{\n                        let [field, messages] = param;\n                        return \"\".concat(field, \": \").concat(messages.join(', '));\n                    }).join('; ');\n                    displayError += \" (Issues: \".concat(issuesString, \")\");\n                }\n                throw new Error(displayError);\n            }\n            // If response IS ok, then parse the successful JSON response\n            const result = await response.json();\n            setNewCustomRoleId('');\n            setNewCustomRoleName('');\n            setNewCustomRoleDescription('');\n            // setShowCreateCustomRoleForm(false); // User might want to add multiple roles\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            // Add the new role optimistically to the local state\n            const newRole = {\n                id: result.id,\n                role_id: result.role_id,\n                name: result.name,\n                description: result.description,\n                user_id: result.user_id,\n                created_at: result.created_at,\n                updated_at: result.updated_at\n            };\n            setUserCustomRoles((prev)=>[\n                    ...prev,\n                    newRole\n                ]);\n            setSuccessMessage(\"Custom role '\".concat(result.name, \"' created successfully! It is now available globally.\"));\n        } catch (err) {\n            setCreateCustomRoleError(err.message);\n        } finally{\n            setIsSavingCustomRole(false);\n        }\n    };\n    const handleDeleteCustomRole = (customRoleDatabaseId, customRoleName)=>{\n        // configId is not needed for deleting a global role\n        if (!customRoleDatabaseId) return;\n        confirmation.showConfirmation({\n            title: 'Delete Custom Role',\n            message: 'Are you sure you want to delete the custom role \"'.concat(customRoleName, \"\\\"? This will unassign it from all API keys where it's currently used. This action cannot be undone.\"),\n            confirmText: 'Delete Role',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setDeletingCustomRoleId(customRoleDatabaseId);\n            setUserCustomRolesError(null);\n            setCreateCustomRoleError(null);\n            setSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles/\".concat(customRoleDatabaseId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json(); // Try to parse JSON for all responses\n                if (!response.ok) {\n                    throw new Error(result.error || 'Failed to delete custom role');\n                }\n                // Optimistically remove from the local state\n                setUserCustomRoles((prev)=>prev.filter((role)=>role.id !== customRoleDatabaseId));\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage(result.message || 'Global custom role \"'.concat(customRoleName, '\" deleted successfully.'));\n                // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.\n                // This ensures the displayed assigned roles for keys on this page are up-to-date.\n                if (configId) {\n                    fetchKeysAndRolesForConfig();\n                }\n            } catch (err) {\n                setUserCustomRolesError(\"Error deleting role: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setDeletingCustomRoleId(null);\n            }\n        });\n    };\n    const renderManageRolesModal = ()=>{\n        if (!editingRolesApiKey) return null;\n        const combinedRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id // The actual DB ID (UUID) for delete operations\n                }))\n        ].sort((a, b)=>a.name.localeCompare(b.name));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: [\n                                    \"Manage Roles for: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-400\",\n                                        children: editingRolesApiKey.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 80\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 996,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 994,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            userCustomRolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 text-sm\",\n                                    children: [\n                                        \"Error with custom roles: \",\n                                        userCustomRolesError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1003,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                feature: \"custom_roles\",\n                                customMessage: \"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCreateCustomRoleForm(!showCreateCustomRoleForm),\n                                            className: \"btn-primary text-sm inline-flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 15\n                                    }, this),\n                                    showCreateCustomRoleForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-md font-medium text-white mb-3\",\n                                                children: \"Create New Custom Role for this Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1024,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleId\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Role ID (short, no spaces, max 30 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleId\",\n                                                                value: newCustomRoleId,\n                                                                onChange: (e)=>setNewCustomRoleId(e.target.value.replace(/\\s/g, '')),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 30,\n                                                                placeholder: \"e.g., my_blog_writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1028,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1026,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleName\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Display Name (max 100 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleName\",\n                                                                value: newCustomRoleName,\n                                                                onChange: (e)=>setNewCustomRoleName(e.target.value),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 100,\n                                                                placeholder: \"e.g., My Awesome Blog Writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1038,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleDescription\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Description (optional, max 500 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"newCustomRoleDescription\",\n                                                                value: newCustomRoleDescription,\n                                                                onChange: (e)=>setNewCustomRoleDescription(e.target.value),\n                                                                rows: 2,\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 500,\n                                                                placeholder: \"Optional: Describe what this role is for...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    createCustomRoleError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-900/50 border border-red-800/50 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 text-sm\",\n                                                            children: createCustomRoleError\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateCustomRole,\n                                                        disabled: isSavingCustomRole,\n                                                        className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1025,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1001,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-300 mb-3\",\n                                children: \"Select roles to assign:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1076,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-y-auto space-y-2\",\n                                style: {\n                                    maxHeight: 'calc(90vh - 350px)'\n                                },\n                                children: [\n                                    isLoadingUserCustomRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm ml-2\",\n                                                children: \"Loading custom roles...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 17\n                                    }, this),\n                                    combinedRoles.map((role)=>{\n                                        const isAssigned = editingRolesApiKey.assigned_roles.some((ar)=>ar.id === role.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 \".concat(isAssigned ? 'bg-orange-500/20 border-orange-500/30 shadow-sm' : 'bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"role-\".concat(role.id),\n                                                    className: \"flex items-center cursor-pointer flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"role-\".concat(role.id),\n                                                            checked: isAssigned,\n                                                            onChange: ()=>handleRoleToggle(editingRolesApiKey, role.id, isAssigned),\n                                                            className: \"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1093,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-sm font-medium \".concat(isAssigned ? 'text-orange-300' : 'text-white'),\n                                                            children: role.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        role.isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300\",\n                                                            children: \"Custom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1092,\n                                                    columnNumber: 21\n                                                }, this),\n                                                role.isCustom && role.databaseId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteCustomRole(role.databaseId, role.name),\n                                                    disabled: deletingCustomRoleId === role.databaseId,\n                                                    className: \"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2\",\n                                                    title: \"Delete this custom role\",\n                                                    children: deletingCustomRoleId === role.databaseId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1116,\n                                                        columnNumber: 70\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1116,\n                                                        columnNumber: 123\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1110,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, role.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1087,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1077,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1075,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"btn-secondary\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1127,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1126,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 993,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 992,\n            columnNumber: 7\n        }, this);\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1142,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoadingConfig && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__.CompactManageKeysLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1146,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1158,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: configDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-6 w-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1170,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1169,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-3xl font-bold text-white\",\n                                                                    children: configDetails.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1173,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400 mt-1\",\n                                                                    children: \"Model Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1180,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"ID: \",\n                                                        configDetails.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : error && !isLoadingConfig ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-red-400\",\n                                                            children: \"Configuration Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 mt-1\",\n                                                            children: error.replace(\"Error loading model configuration: \", \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1185,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-gray-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"Loading Configuration...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1200,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 mt-1\",\n                                                            children: \"Please wait while we fetch your model details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1199,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1195,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1165,\n                                        columnNumber: 13\n                                    }, this),\n                                    configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateOptimistically(\"/routing-setup/\".concat(configId, \"?from=model-config\")),\n                                            className: \"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group\",\n                                            ...createRoutingHoverPrefetch(configId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Advanced Routing Setup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1213,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1164,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1163,\n                            columnNumber: 11\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1230,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-300 font-medium\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1229,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1228,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1238,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-300 font-medium\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1239,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1237,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1236,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1153,\n                    columnNumber: 9\n                }, this),\n                configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('provider-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'provider-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Provider API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1258,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('user-api-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'user-api-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Generated API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1248,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'provider-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1287,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"Add Provider API Key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Configure new provider key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1291,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1285,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSaveKey,\n                                                className: \"space-y-5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"provider\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Provider\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1298,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"provider\",\n                                                                        value: provider,\n                                                                        onChange: (e)=>{\n                                                                            setProvider(e.target.value);\n                                                                        },\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm\",\n                                                                        children: PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                className: \"bg-gray-800 text-white\",\n                                                                                children: option.label\n                                                                            }, option.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1308,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1301,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1297,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"apiKeyRaw\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"API Key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1314,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"apiKeyRaw\",\n                                                                        type: \"password\",\n                                                                        value: apiKeyRaw,\n                                                                        onChange: (e)=>setApiKeyRaw(e.target.value),\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm\",\n                                                                        placeholder: \"Enter your API key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1317,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1328,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Fetching models...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1327,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                                                                        children: fetchProviderModelsError\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1333,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1313,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"predefinedModelId\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Model Variant\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1338,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"predefinedModelId\",\n                                                                        value: predefinedModelId,\n                                                                        onChange: (e)=>setPredefinedModelId(e.target.value),\n                                                                        disabled: !modelOptions.length,\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500\",\n                                                                        children: modelOptions.length > 0 ? modelOptions.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: m.value,\n                                                                                className: \"bg-gray-800 text-white\",\n                                                                                children: m.label\n                                                                            }, m.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1350,\n                                                                                columnNumber: 27\n                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            disabled: true,\n                                                                            className: \"bg-gray-800 text-gray-400\",\n                                                                            children: fetchedProviderModels === null && isFetchingProviderModels ? \"Loading models...\" : \"Select a provider first\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1353,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1341,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1337,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"label\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Label\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"label\",\n                                                                        value: label,\n                                                                        onChange: (e)=>setLabel(e.target.value),\n                                                                        required: true,\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm\",\n                                                                        placeholder: \"e.g., My OpenAI GPT-4o Key #1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1362,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"temperature\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: [\n                                                                            \"Temperature\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-400 ml-1\",\n                                                                                children: \"(0.0 - 2.0)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1376,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                id: \"temperature\",\n                                                                                min: \"0\",\n                                                                                max: \"2\",\n                                                                                step: \"0.1\",\n                                                                                value: temperature,\n                                                                                onChange: (e)=>setTemperature(parseFloat(e.target.value)),\n                                                                                className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1379,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: \"Conservative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1390,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"number\",\n                                                                                            min: \"0\",\n                                                                                            max: \"2\",\n                                                                                            step: \"0.1\",\n                                                                                            value: temperature,\n                                                                                            onChange: (e)=>setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0))),\n                                                                                            className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1392,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1391,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: \"Creative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1402,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1389,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1404,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1373,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim(),\n                                                        className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm\",\n                                                        children: isSavingKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1418,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Saving...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1417,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1423,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Add API Key\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1411,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1433,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-blue-300 mb-1\",\n                                                                    children: \"Key Configuration Rules\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1435,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same API key, different models:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1437,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1437,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Different API keys, same model:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1438,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"❌ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same model twice:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1439,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Not allowed in one configuration\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1439,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1436,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1434,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1432,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1431,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1284,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1452,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1451,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"API Keys & Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1455,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Manage existing keys\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1456,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1450,\n                                                columnNumber: 15\n                                            }, this),\n                                            isLoadingKeysAndRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Loading API keys...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1463,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1461,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || error && error.startsWith(\"Error loading model configuration:\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1470,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1469,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-1\",\n                                                        children: \"No API Keys\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1472,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Add your first key using the form\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1473,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1468,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                                children: savedKeysWithRoles.map((key, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in\",\n                                                        style: {\n                                                            animationDelay: \"\".concat(index * 50, \"ms\")\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-sm font-semibold text-white truncate mr-2\",\n                                                                                    children: key.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1484,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1487,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        \"Default\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1486,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1483,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: [\n                                                                                                key.provider,\n                                                                                                \" (\",\n                                                                                                key.predefined_model_id,\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1495,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50\",\n                                                                                            children: [\n                                                                                                \"Temp: \",\n                                                                                                key.temperature\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1498,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1494,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                                    feature: \"custom_roles\",\n                                                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: \"Roles available on Starter plan+\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1507,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1506,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: key.assigned_roles.length > 0 ? key.assigned_roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50\",\n                                                                                                children: role.name\n                                                                                            }, role.id, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                                lineNumber: 1516,\n                                                                                                columnNumber: 37\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: \"No roles\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1521,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1513,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1503,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1493,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        !key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSetDefaultChatKey(key.id),\n                                                                            className: \"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Set as default chat model\",\n                                                                            children: \"Set Default\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1528,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1482,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 ml-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleEditKey(key),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Edit Model & Settings\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1547,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1540,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                            feature: \"custom_roles\",\n                                                                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                disabled: true,\n                                                                                className: \"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Role management requires Starter plan or higher\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1558,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1552,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setEditingRolesApiKey(key),\n                                                                                disabled: isDeletingKey === key.id,\n                                                                                className: \"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Manage Roles\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1569,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1562,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1549,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteKey(key.id, key.label),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Delete Key\",\n                                                                            children: isDeletingKey === key.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1580,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1582,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1572,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1539,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1481,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, key.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1480,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1478,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && error && !error.startsWith(\"Error loading model configuration:\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1595,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 font-medium text-sm\",\n                                                            children: [\n                                                                \"Could not load API keys/roles: \",\n                                                                error\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1596,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1594,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1593,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1449,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1448,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1281,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'user-api-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__.ApiKeyManager, {\n                                configId: configId,\n                                configName: configDetails.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1608,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1607,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1246,\n                    columnNumber: 9\n                }, this),\n                editingRolesApiKey && renderManageRolesModal(),\n                editingApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Edit API Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1625,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingApiKey(null),\n                                        className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1630,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1626,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1624,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-2\",\n                                                children: editingApiKey.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"Current: \",\n                                                    editingApiKey.provider,\n                                                    \" (\",\n                                                    editingApiKey.predefined_model_id,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1637,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1635,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Provider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1644,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300\",\n                                                        children: ((_llmProviders_find = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find((p)=>p.id === editingApiKey.provider)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.name) || editingApiKey.provider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1647,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: \"Provider cannot be changed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1650,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1643,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editModelId\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"editModelId\",\n                                                        value: editPredefinedModelId,\n                                                        onChange: (e)=>setEditPredefinedModelId(e.target.value),\n                                                        disabled: !editModelOptions.length,\n                                                        className: \"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30\",\n                                                        children: editModelOptions.length > 0 ? editModelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1666,\n                                                                columnNumber: 25\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            disabled: true,\n                                                            children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1671,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1653,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editTemperature\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: [\n                                                            \"Temperature: \",\n                                                            editTemperature\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1679,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        id: \"editTemperature\",\n                                                        min: \"0\",\n                                                        max: \"2\",\n                                                        step: \"0.1\",\n                                                        value: editTemperature,\n                                                        onChange: (e)=>setEditTemperature(parseFloat(e.target.value)),\n                                                        className: \"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1682,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"0.0 (Focused)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1693,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"1.0 (Balanced)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1694,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"2.0 (Creative)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1695,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1692,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1678,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1700,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1699,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1642,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1634,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setEditingApiKey(null),\n                                            className: \"btn-secondary\",\n                                            disabled: isSavingEdit,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1709,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSaveEdit,\n                                            disabled: isSavingEdit,\n                                            className: \"btn-primary\",\n                                            children: isSavingEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1723,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Saving...\"\n                                                ]\n                                            }, void 0, true) : 'Save Changes'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1716,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1708,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1707,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1623,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1622,\n                    columnNumber: 9\n                }, this),\n                !configDetails && !isLoadingConfig && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"h-8 w-8 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1739,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1738,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                            children: \"Model Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1741,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-8\",\n                            children: \"This API Model configuration could not be found or may have been deleted.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1742,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1747,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1743,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1737,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1754,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                    id: \"global-tooltip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1766,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1151,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1150,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigDetailsPage, \"phTjtpqKEcxfO5MxUFWlO/AjVB0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe,\n        _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = ConfigDetailsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"ConfigDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/my-models/[configId]/page.tsx\n"));

/***/ })

});