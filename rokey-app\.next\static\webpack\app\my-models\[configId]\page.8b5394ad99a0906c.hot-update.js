"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/my-models/[configId]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/my-models/[configId]/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfigDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useManageKeysPrefetch */ \"(app-pages-browser)/./src/hooks/useManageKeysPrefetch.ts\");\n/* harmony import */ var _components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ManageKeysLoadingSkeleton */ \"(app-pages-browser)/./src/components/ManageKeysLoadingSkeleton.tsx\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/UserApiKeys/ApiKeyManager */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n // For accessing route params\n // Ensure path is correct\n\n\n\n\n\n\n\n\n\n\n\n// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction ConfigDetailsPage() {\n    var _PROVIDER_OPTIONS_, _llmProviders_find;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const configId = params.configId;\n    // Confirmation modal hook\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation)();\n    // Navigation hook with safe context\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe)();\n    const navigateOptimistically = (navigationContext === null || navigationContext === void 0 ? void 0 : navigationContext.navigateOptimistically) || ((href)=>{\n        window.location.href = href;\n    });\n    // Prefetch hooks\n    const { getCachedData, isCached, clearCache } = (0,_hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch)();\n    const { createHoverPrefetch: createRoutingHoverPrefetch } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch)();\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingConfig, setIsLoadingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Provider state now stores the slug (p.id)\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai'); // Stores slug\n    const [predefinedModelId, setPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [apiKeyRaw, setApiKeyRaw] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [label, setLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [temperature, setTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [isSavingKey, setIsSavingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for dynamic model fetching\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [savedKeysWithRoles, setSavedKeysWithRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeletingKey, setIsDeletingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingRolesApiKey, setEditingRolesApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for editing API keys\n    const [editingApiKey, setEditingApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTemperature, setEditTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [editPredefinedModelId, setEditPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingEdit, setIsSavingEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for User-Defined Custom Roles\n    const [userCustomRoles, setUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCustomRolesError, setUserCustomRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newCustomRoleId, setNewCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleName, setNewCustomRoleName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleDescription, setNewCustomRoleDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingCustomRole, setIsSavingCustomRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createCustomRoleError, setCreateCustomRoleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCustomRoleId, setDeletingCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // stores the DB ID (UUID) of the custom role\n    // State for tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('provider-keys');\n    // Browsing configuration state\n    const [browsingEnabled, setBrowsingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [browsingModels, setBrowsingModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSavingBrowsing, setIsSavingBrowsing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch config details with optimistic loading\n    const fetchConfigDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchConfigDetails]\": async ()=>{\n            if (!configId) return;\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached config data for: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                // Load browsing settings from cached config\n                if (cachedData.configDetails.browsing_enabled !== undefined) {\n                    setBrowsingEnabled(cachedData.configDetails.browsing_enabled);\n                }\n                if (cachedData.configDetails.browsing_models) {\n                    setBrowsingModels(cachedData.configDetails.browsing_models);\n                }\n                setIsLoadingConfig(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoadingConfig(true);\n            setError(null);\n            try {\n                const res = await fetch(\"/api/custom-configs\");\n                if (!res.ok) {\n                    const errData = await res.json();\n                    throw new Error(errData.error || 'Failed to fetch configurations list');\n                }\n                const allConfigs = await res.json();\n                const currentConfig = allConfigs.find({\n                    \"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\": (c)=>c.id === configId\n                }[\"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\"]);\n                if (!currentConfig) throw new Error('Configuration not found in the list.');\n                setConfigDetails(currentConfig);\n                // Load browsing settings from fetched config\n                if (currentConfig.browsing_enabled !== undefined) {\n                    setBrowsingEnabled(currentConfig.browsing_enabled);\n                }\n                if (currentConfig.browsing_models) {\n                    setBrowsingModels(currentConfig.browsing_models);\n                }\n            } catch (err) {\n                setError(\"Error loading model configuration: \".concat(err.message));\n                setConfigDetails(null);\n            } finally{\n                setIsLoadingConfig(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchConfigDetails]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            fetchConfigDetails();\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        fetchConfigDetails\n    ]);\n    // New: Function to fetch all models from the database with caching\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.models) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached models data for: \".concat(configId));\n                setFetchedProviderModels(cachedData.models);\n                setIsFetchingProviderModels(false);\n                return;\n            }\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                // The new API doesn't need a specific provider or API key in the body to list models\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                setFetchProviderModelsError(\"Error fetching models: \".concat(err.message));\n                setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\"], [\n        configId,\n        getCachedData\n    ]);\n    // New: Fetch all models from DB when configId is available (i.e., page is ready)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configId) {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configId,\n        fetchModelsFromDatabase\n    ]);\n    // Updated: Function to fetch all global custom roles for the authenticated user with caching\n    const fetchUserCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.userCustomRoles) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached custom roles data for: \".concat(configId));\n                setUserCustomRoles(cachedData.userCustomRoles);\n                setIsLoadingUserCustomRoles(false);\n                return;\n            }\n            setIsLoadingUserCustomRoles(true);\n            setUserCustomRolesError(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles\"); // New global endpoint\n                if (!response.ok) {\n                    let errorData;\n                    try {\n                        errorData = await response.json(); // Attempt to parse error as JSON\n                    } catch (e) {\n                        // If error response is not JSON, use text or a generic error\n                        const errorText = await response.text().catch({\n                            \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": ()=>\"HTTP error \".concat(response.status)\n                        }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"]);\n                        errorData = {\n                            error: errorText\n                        };\n                    }\n                    const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : \"Failed to fetch custom roles (status: \".concat(response.status, \")\"));\n                    if (response.status === 401) {\n                        setUserCustomRolesError(errorMessage);\n                    } else {\n                        throw new Error(errorMessage); // Throw for other errors to be caught by the main catch\n                    }\n                    setUserCustomRoles([]); // Clear roles if there was an error handled here\n                } else {\n                    // Only call .json() here if response.ok and body hasn't been read\n                    const data = await response.json();\n                    setUserCustomRoles(data);\n                // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try\n                }\n            } catch (err) {\n                // This catch handles network errors from fetch() or errors thrown from !response.ok block\n                setUserCustomRolesError(err.message);\n                setUserCustomRoles([]); // Clear roles on error\n            } finally{\n                setIsLoadingUserCustomRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"], []);\n    // Fetch API keys and their roles for this config with optimistic loading\n    const fetchKeysAndRolesForConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": async ()=>{\n            if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached keys data for: \".concat(configId));\n                // Process cached keys with roles (same logic as below)\n                const keysWithRolesPromises = cachedData.apiKeys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean);\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: cachedData.defaultChatKeyId === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n                setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);\n                setIsLoadingKeysAndRoles(false);\n                return;\n            }\n            setIsLoadingKeysAndRoles(true);\n            // Preserve config loading errors, clear others\n            setError({\n                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev && prev.startsWith('Error loading model configuration:') ? prev : null\n            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]);\n            setSuccessMessage(null);\n            try {\n                // Fetch all keys for the config\n                const keysResponse = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysResponse.ok) {\n                    const errorData = await keysResponse.json();\n                    throw new Error(errorData.error || 'Failed to fetch API keys');\n                }\n                const keys = await keysResponse.json();\n                // Fetch default general chat key\n                const defaultKeyResponse = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-chat-key\"));\n                if (!defaultKeyResponse.ok) {\n                    console.warn('Failed to fetch default chat key info');\n                }\n                const defaultKeyData = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;\n                setDefaultGeneralChatKeyId((defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) || null);\n                // For each key, fetch its assigned roles\n                const keysWithRolesPromises = keys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    // 1. Check predefined roles\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    // 2. Check current user's global custom roles\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean); // filter(Boolean) removes null entries\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: (defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n            } catch (err) {\n                setError({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev ? \"\".concat(prev, \"; \").concat(err.message) : err.message\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]); // Append if there was a config load error\n            } finally{\n                setIsLoadingKeysAndRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"], [\n        configId,\n        userCustomRoles\n    ]); // Added userCustomRoles to dependency array\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configDetails) {\n                fetchUserCustomRoles(); // Call to fetch custom roles\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        fetchUserCustomRoles\n    ]); // Only depends on configDetails and the stable fetchUserCustomRoles\n    // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Ensure userCustomRoles is not in its initial undefined/null state from useState([])\n            // and actually contains data (or an empty array confirming fetch completion)\n            if (configDetails && userCustomRoles) {\n                fetchKeysAndRolesForConfig();\n            }\n        // This effect runs if configDetails changes, userCustomRoles (state) changes,\n        // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).\n        // This is the desired behavior: re-fetch keys/roles if custom roles change.\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        userCustomRoles,\n        fetchKeysAndRolesForConfig\n    ]);\n    // Updated: Memoize model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === provider\n                }[\"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                // as an OpenRouter key can access any of them.\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    // If for some reason the specific models are not found in fetchedProviderModels,\n                    // it's better to return an empty array or a message than all DeepSeek models unfiltered.\n                    // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.\n                    // For now, strictly showing only these two if found.\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n            }\n            return []; // Return empty array if models haven't been fetched or if fetch failed.\n        }\n    }[\"ConfigDetailsPage.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        provider\n    ]);\n    // Model options for edit modal - filtered by the current key's provider\n    const editModelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[editModelOptions]\": ()=>{\n            if (fetchedProviderModels && editingApiKey) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\": (p)=>p.id === editingApiKey.provider\n                }[\"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"], [\n        fetchedProviderModels,\n        editingApiKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Auto-select the first model from the dynamic modelOptions when provider changes or models load\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            } else {\n                setPredefinedModelId(''); // Clear if no models for provider\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        modelOptions,\n        provider\n    ]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider\n    // Fetch models based on the provider's slug (p.id)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (provider) {\n                // Logic to fetch models for the selected provider slug might need adjustment\n                // if it was previously relying on the provider display name.\n                // Assuming fetchProviderModels is adapted or already uses slugs.\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        provider,\n        fetchModelsFromDatabase\n    ]);\n    const handleSaveKey = async (e)=>{\n        e.preventDefault();\n        if (!configId) {\n            setError('Configuration ID is missing.');\n            return;\n        }\n        // Frontend validation: Check for duplicate models\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.predefined_model_id === predefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');\n            return;\n        }\n        setIsSavingKey(true);\n        setError(null);\n        setSuccessMessage(null);\n        // provider state variable already holds the slug\n        const newKeyData = {\n            custom_api_config_id: configId,\n            provider,\n            predefined_model_id: predefinedModelId,\n            api_key_raw: apiKeyRaw,\n            label,\n            temperature\n        };\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        try {\n            var _PROVIDER_OPTIONS_;\n            const response = await fetch('/api/keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newKeyData)\n            });\n            const result = await response.json();\n            if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');\n            // Create optimistic key object with the returned data\n            const newKey = {\n                id: result.id,\n                custom_api_config_id: configId,\n                provider,\n                predefined_model_id: predefinedModelId,\n                label,\n                temperature,\n                status: 'active',\n                created_at: new Date().toISOString(),\n                last_used_at: null,\n                is_default_general_chat_model: false,\n                assigned_roles: []\n            };\n            // Optimistically add the new key to the list\n            setSavedKeysWithRoles((prevKeys)=>[\n                    ...prevKeys,\n                    newKey\n                ]);\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(label, '\" saved successfully!'));\n            setProvider(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai');\n            setApiKeyRaw('');\n            setLabel('');\n            setTemperature(1.0);\n            // Reset model selection to first available option\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            }\n        } catch (err) {\n            // Revert UI on error\n            setSavedKeysWithRoles(previousKeysState);\n            setError(\"Save Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingKey(false);\n        }\n    };\n    const handleEditKey = (key)=>{\n        setEditingApiKey(key);\n        setEditTemperature(key.temperature || 1.0);\n        setEditPredefinedModelId(key.predefined_model_id);\n    };\n    const handleSaveEdit = async ()=>{\n        if (!editingApiKey) return;\n        // Frontend validation: Check for duplicate models (excluding the current key being edited)\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration.');\n            return;\n        }\n        setIsSavingEdit(true);\n        setError(null);\n        setSuccessMessage(null);\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === editingApiKey.id) {\n                    return {\n                        ...key,\n                        temperature: editTemperature,\n                        predefined_model_id: editPredefinedModelId\n                    };\n                }\n                return key;\n            }));\n        try {\n            const response = await fetch(\"/api/keys?id=\".concat(editingApiKey.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    temperature: editTemperature,\n                    predefined_model_id: editPredefinedModelId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                throw new Error(result.details || result.error || 'Failed to update API key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(editingApiKey.label, '\" updated successfully!'));\n            setEditingApiKey(null);\n        } catch (err) {\n            setError(\"Update Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingEdit(false);\n        }\n    };\n    const handleDeleteKey = (keyId, keyLabel)=>{\n        confirmation.showConfirmation({\n            title: 'Delete API Key',\n            message: 'Are you sure you want to delete the API key \"'.concat(keyLabel, '\"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),\n            confirmText: 'Delete API Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setIsDeletingKey(keyId);\n            setError(null);\n            setSuccessMessage(null);\n            // Store previous state for rollback on error\n            const previousKeysState = [\n                ...savedKeysWithRoles\n            ];\n            const previousDefaultKeyId = defaultGeneralChatKeyId;\n            const keyToDelete = savedKeysWithRoles.find((key)=>key.id === keyId);\n            // Optimistic UI update - immediately remove the key from the list\n            setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n            // If the deleted key was the default, clear the default\n            if (keyToDelete === null || keyToDelete === void 0 ? void 0 : keyToDelete.is_default_general_chat_model) {\n                setDefaultGeneralChatKeyId(null);\n            }\n            try {\n                const response = await fetch(\"/api/keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (!response.ok) {\n                    // Revert UI on error\n                    setSavedKeysWithRoles(previousKeysState);\n                    setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                    // Special handling for 404 errors (key already deleted)\n                    if (response.status === 404) {\n                        // Key was already deleted, so the optimistic update was correct\n                        // Don't revert the UI, just show a different message\n                        setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n                        setSuccessMessage('API key \"'.concat(keyLabel, '\" was already deleted.'));\n                        return; // Don't throw error\n                    }\n                    throw new Error(result.details || result.error || 'Failed to delete API key');\n                }\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage('API key \"'.concat(keyLabel, '\" deleted successfully!'));\n            } catch (err) {\n                setError(\"Delete Key Error: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setIsDeletingKey(null);\n            }\n        });\n    };\n    const handleSetDefaultChatKey = async (apiKeyIdToSet)=>{\n        if (!configId) return;\n        setError(null);\n        setSuccessMessage(null);\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ]; // Keep a copy in case of error\n        const previousDefaultKeyId = defaultGeneralChatKeyId;\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>({\n                    ...key,\n                    is_default_general_chat_model: key.id === apiKeyIdToSet\n                })));\n        setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-key-handler/\").concat(apiKeyIdToSet), {\n                method: 'PUT'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState.map((k)=>({\n                        ...k\n                    }))); // Ensure deep copy for re-render\n                setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                throw new Error(result.details || result.error || 'Failed to set default chat key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage(result.message || 'Default general chat key updated!');\n        } catch (err) {\n            setError(\"Set Default Error: \".concat(err.message));\n        }\n    };\n    const handleRoleToggle = async (apiKey, roleId, isAssigned)=>{\n        setError(null);\n        setSuccessMessage(null);\n        const endpoint = \"/api/keys/\".concat(apiKey.id, \"/roles\");\n        // For optimistic update, find role details from combined list (predefined or user's global custom roles)\n        const allAvailableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id\n                }))\n        ];\n        const roleDetails = allAvailableRoles.find((r)=>r.id === roleId) || {\n            id: roleId,\n            name: roleId,\n            description: ''\n        };\n        const previousKeysState = savedKeysWithRoles.map((k)=>({\n                ...k,\n                assigned_roles: [\n                    ...k.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            })); // Deep copy\n        let previousEditingRolesApiKey = null;\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            previousEditingRolesApiKey = {\n                ...editingRolesApiKey,\n                assigned_roles: [\n                    ...editingRolesApiKey.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            };\n        }\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === apiKey.id) {\n                    const updatedRoles = isAssigned ? key.assigned_roles.filter((r)=>r.id !== roleId) : [\n                        ...key.assigned_roles,\n                        roleDetails\n                    ];\n                    return {\n                        ...key,\n                        assigned_roles: updatedRoles\n                    };\n                }\n                return key;\n            }));\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            setEditingRolesApiKey((prevEditingKey)=>{\n                if (!prevEditingKey) return null;\n                const updatedRoles = isAssigned ? prevEditingKey.assigned_roles.filter((r)=>r.id !== roleId) : [\n                    ...prevEditingKey.assigned_roles,\n                    roleDetails\n                ];\n                return {\n                    ...prevEditingKey,\n                    assigned_roles: updatedRoles\n                };\n            });\n        }\n        try {\n            let response;\n            if (isAssigned) {\n                response = await fetch(\"\".concat(endpoint, \"/\").concat(roleId), {\n                    method: 'DELETE'\n                });\n            } else {\n                response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        role_name: roleId\n                    })\n                });\n            }\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                if (previousEditingRolesApiKey) {\n                    setEditingRolesApiKey(previousEditingRolesApiKey);\n                } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n                    const originalKeyData = previousKeysState.find((k)=>k.id === apiKey.id);\n                    if (originalKeyData) setEditingRolesApiKey(originalKeyData);\n                }\n                // Use the error message from the backend if available (e.g., for 409 conflict)\n                const errorMessage = response.status === 409 && result.error ? result.error : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');\n                throw new Error(errorMessage);\n            }\n            setSuccessMessage(result.message || \"Role '\".concat(roleDetails.name, \"' \").concat(isAssigned ? 'unassigned' : 'assigned', \" successfully.\"));\n        } catch (err) {\n            // err.message now contains the potentially more user-friendly message from the backend or a fallback\n            setError(\"Role Update Error: \".concat(err.message));\n        }\n    };\n    const handleCreateCustomRole = async ()=>{\n        // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.\n        // configId is also not needed for creating a global role.\n        if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {\n            setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');\n            return;\n        }\n        // Check against PREDEFINED_ROLES and the user's existing global custom roles\n        if (_config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.some((pr)=>pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || userCustomRoles.some((cr)=>cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {\n            setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');\n            return;\n        }\n        if (!newCustomRoleName.trim()) {\n            setCreateCustomRoleError('Role Name is required.');\n            return;\n        }\n        setCreateCustomRoleError(null);\n        setIsSavingCustomRole(true);\n        try {\n            const response = await fetch(\"/api/user/custom-roles\", {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    role_id: newCustomRoleId.trim(),\n                    name: newCustomRoleName.trim(),\n                    description: newCustomRoleDescription.trim()\n                })\n            });\n            if (!response.ok) {\n                // Try to parse the error response as JSON, but fallback if it's not JSON\n                let errorResult;\n                try {\n                    errorResult = await response.json();\n                } catch (parseError) {\n                    // If JSON parsing fails, use the response text or a generic status message\n                    const errorText = await response.text().catch(()=>\"HTTP status \".concat(response.status));\n                    errorResult = {\n                        error: \"Server error, could not parse response.\",\n                        details: errorText\n                    };\n                }\n                let displayError = errorResult.error || 'Failed to create custom role.';\n                if (errorResult.details) {\n                    displayError += \" (Details: \".concat(errorResult.details, \")\");\n                } else if (errorResult.issues) {\n                    // If Zod issues, format them for better readability\n                    const issuesString = Object.entries(errorResult.issues).map((param)=>{\n                        let [field, messages] = param;\n                        return \"\".concat(field, \": \").concat(messages.join(', '));\n                    }).join('; ');\n                    displayError += \" (Issues: \".concat(issuesString, \")\");\n                }\n                throw new Error(displayError);\n            }\n            // If response IS ok, then parse the successful JSON response\n            const result = await response.json();\n            setNewCustomRoleId('');\n            setNewCustomRoleName('');\n            setNewCustomRoleDescription('');\n            // setShowCreateCustomRoleForm(false); // User might want to add multiple roles\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            // Add the new role optimistically to the local state\n            const newRole = {\n                id: result.id,\n                role_id: result.role_id,\n                name: result.name,\n                description: result.description,\n                user_id: result.user_id,\n                created_at: result.created_at,\n                updated_at: result.updated_at\n            };\n            setUserCustomRoles((prev)=>[\n                    ...prev,\n                    newRole\n                ]);\n            setSuccessMessage(\"Custom role '\".concat(result.name, \"' created successfully! It is now available globally.\"));\n        } catch (err) {\n            setCreateCustomRoleError(err.message);\n        } finally{\n            setIsSavingCustomRole(false);\n        }\n    };\n    const handleDeleteCustomRole = (customRoleDatabaseId, customRoleName)=>{\n        // configId is not needed for deleting a global role\n        if (!customRoleDatabaseId) return;\n        confirmation.showConfirmation({\n            title: 'Delete Custom Role',\n            message: 'Are you sure you want to delete the custom role \"'.concat(customRoleName, \"\\\"? This will unassign it from all API keys where it's currently used. This action cannot be undone.\"),\n            confirmText: 'Delete Role',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setDeletingCustomRoleId(customRoleDatabaseId);\n            setUserCustomRolesError(null);\n            setCreateCustomRoleError(null);\n            setSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles/\".concat(customRoleDatabaseId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json(); // Try to parse JSON for all responses\n                if (!response.ok) {\n                    throw new Error(result.error || 'Failed to delete custom role');\n                }\n                // Optimistically remove from the local state\n                setUserCustomRoles((prev)=>prev.filter((role)=>role.id !== customRoleDatabaseId));\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage(result.message || 'Global custom role \"'.concat(customRoleName, '\" deleted successfully.'));\n                // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.\n                // This ensures the displayed assigned roles for keys on this page are up-to-date.\n                if (configId) {\n                    fetchKeysAndRolesForConfig();\n                }\n            } catch (err) {\n                setUserCustomRolesError(\"Error deleting role: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setDeletingCustomRoleId(null);\n            }\n        });\n    };\n    const renderManageRolesModal = ()=>{\n        if (!editingRolesApiKey) return null;\n        const combinedRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id // The actual DB ID (UUID) for delete operations\n                }))\n        ].sort((a, b)=>a.name.localeCompare(b.name));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: [\n                                    \"Manage Roles for: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-400\",\n                                        children: editingRolesApiKey.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 80\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1014,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1015,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            userCustomRolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 text-sm\",\n                                    children: [\n                                        \"Error with custom roles: \",\n                                        userCustomRolesError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1022,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                feature: \"custom_roles\",\n                                customMessage: \"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCreateCustomRoleForm(!showCreateCustomRoleForm),\n                                            className: \"btn-primary text-sm inline-flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 15\n                                    }, this),\n                                    showCreateCustomRoleForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-md font-medium text-white mb-3\",\n                                                children: \"Create New Custom Role for this Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleId\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Role ID (short, no spaces, max 30 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1046,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleId\",\n                                                                value: newCustomRoleId,\n                                                                onChange: (e)=>setNewCustomRoleId(e.target.value.replace(/\\s/g, '')),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 30,\n                                                                placeholder: \"e.g., my_blog_writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleName\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Display Name (max 100 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleName\",\n                                                                value: newCustomRoleName,\n                                                                onChange: (e)=>setNewCustomRoleName(e.target.value),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 100,\n                                                                placeholder: \"e.g., My Awesome Blog Writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1055,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleDescription\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Description (optional, max 500 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1066,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"newCustomRoleDescription\",\n                                                                value: newCustomRoleDescription,\n                                                                onChange: (e)=>setNewCustomRoleDescription(e.target.value),\n                                                                rows: 2,\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 500,\n                                                                placeholder: \"Optional: Describe what this role is for...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1067,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1065,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    createCustomRoleError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-900/50 border border-red-800/50 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 text-sm\",\n                                                            children: createCustomRoleError\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateCustomRole,\n                                                        disabled: isSavingCustomRole,\n                                                        className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1027,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1020,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-300 mb-3\",\n                                children: \"Select roles to assign:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1095,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-y-auto space-y-2\",\n                                style: {\n                                    maxHeight: 'calc(90vh - 350px)'\n                                },\n                                children: [\n                                    isLoadingUserCustomRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm ml-2\",\n                                                children: \"Loading custom roles...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1098,\n                                        columnNumber: 17\n                                    }, this),\n                                    combinedRoles.map((role)=>{\n                                        const isAssigned = editingRolesApiKey.assigned_roles.some((ar)=>ar.id === role.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 \".concat(isAssigned ? 'bg-orange-500/20 border-orange-500/30 shadow-sm' : 'bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"role-\".concat(role.id),\n                                                    className: \"flex items-center cursor-pointer flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"role-\".concat(role.id),\n                                                            checked: isAssigned,\n                                                            onChange: ()=>handleRoleToggle(editingRolesApiKey, role.id, isAssigned),\n                                                            className: \"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1112,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-sm font-medium \".concat(isAssigned ? 'text-orange-300' : 'text-white'),\n                                                            children: role.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        role.isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300\",\n                                                            children: \"Custom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1123,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1111,\n                                                    columnNumber: 21\n                                                }, this),\n                                                role.isCustom && role.databaseId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteCustomRole(role.databaseId, role.name),\n                                                    disabled: deletingCustomRoleId === role.databaseId,\n                                                    className: \"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2\",\n                                                    title: \"Delete this custom role\",\n                                                    children: deletingCustomRoleId === role.databaseId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 70\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 123\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1129,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, role.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1096,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1094,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"btn-secondary\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1146,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1012,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1011,\n            columnNumber: 7\n        }, this);\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1161,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoadingConfig && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__.CompactManageKeysLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1165,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: configDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-6 w-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1189,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1188,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-3xl font-bold text-white\",\n                                                                    children: configDetails.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1192,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400 mt-1\",\n                                                                    children: \"Model Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1195,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1187,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1199,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"ID: \",\n                                                        configDetails.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : error && !isLoadingConfig ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-red-400\",\n                                                            children: \"Configuration Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1209,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 mt-1\",\n                                                            children: error.replace(\"Error loading model configuration: \", \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1204,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-gray-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1216,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"Loading Configuration...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 mt-1\",\n                                                            children: \"Please wait while we fetch your model details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1220,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1214,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 13\n                                    }, this),\n                                    configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateOptimistically(\"/routing-setup/\".concat(configId, \"?from=model-config\")),\n                                            className: \"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group\",\n                                            ...createRoutingHoverPrefetch(configId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Advanced Routing Setup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1183,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1182,\n                            columnNumber: 11\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-300 font-medium\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1248,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1247,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-300 font-medium\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1256,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1255,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1172,\n                    columnNumber: 9\n                }, this),\n                configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('provider-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'provider-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Provider API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1277,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('user-api-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'user-api-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Generated API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1290,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1268,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1267,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'provider-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1306,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"Add Provider API Key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1309,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Configure new provider key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1310,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1308,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSaveKey,\n                                                className: \"space-y-5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"provider\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Provider\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1317,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"provider\",\n                                                                        value: provider,\n                                                                        onChange: (e)=>{\n                                                                            setProvider(e.target.value);\n                                                                        },\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm\",\n                                                                        children: PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                className: \"bg-gray-800 text-white\",\n                                                                                children: option.label\n                                                                            }, option.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1327,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1316,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"apiKeyRaw\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"API Key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1333,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"apiKeyRaw\",\n                                                                        type: \"password\",\n                                                                        value: apiKeyRaw,\n                                                                        onChange: (e)=>setApiKeyRaw(e.target.value),\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm\",\n                                                                        placeholder: \"Enter your API key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1336,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1347,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Fetching models...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1346,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                                                                        children: fetchProviderModelsError\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1352,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1332,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"predefinedModelId\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Model Variant\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1357,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"predefinedModelId\",\n                                                                        value: predefinedModelId,\n                                                                        onChange: (e)=>setPredefinedModelId(e.target.value),\n                                                                        disabled: !modelOptions.length,\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500\",\n                                                                        children: modelOptions.length > 0 ? modelOptions.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: m.value,\n                                                                                className: \"bg-gray-800 text-white\",\n                                                                                children: m.label\n                                                                            }, m.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1369,\n                                                                                columnNumber: 27\n                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            disabled: true,\n                                                                            className: \"bg-gray-800 text-gray-400\",\n                                                                            children: fetchedProviderModels === null && isFetchingProviderModels ? \"Loading models...\" : \"Select a provider first\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1372,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1360,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"label\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Label\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"label\",\n                                                                        value: label,\n                                                                        onChange: (e)=>setLabel(e.target.value),\n                                                                        required: true,\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm\",\n                                                                        placeholder: \"e.g., My OpenAI GPT-4o Key #1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1381,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"temperature\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: [\n                                                                            \"Temperature\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-400 ml-1\",\n                                                                                children: \"(0.0 - 2.0)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1395,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                id: \"temperature\",\n                                                                                min: \"0\",\n                                                                                max: \"2\",\n                                                                                step: \"0.1\",\n                                                                                value: temperature,\n                                                                                onChange: (e)=>setTemperature(parseFloat(e.target.value)),\n                                                                                className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1398,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: \"Conservative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1409,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"number\",\n                                                                                            min: \"0\",\n                                                                                            max: \"2\",\n                                                                                            step: \"0.1\",\n                                                                                            value: temperature,\n                                                                                            onChange: (e)=>setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0))),\n                                                                                            className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1411,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1410,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: \"Creative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1421,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1408,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1423,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1397,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1315,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim(),\n                                                        className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm\",\n                                                        children: isSavingKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1437,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Saving...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1436,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1442,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Add API Key\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1430,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1314,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1452,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-blue-300 mb-1\",\n                                                                    children: \"Key Configuration Rules\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1454,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same API key, different models:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1456,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1456,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Different API keys, same model:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1457,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1457,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"❌ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same model twice:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1458,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Not allowed in one configuration\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1458,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1455,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1453,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1451,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1450,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1303,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1471,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"API Keys & Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1474,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Manage existing keys\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1475,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1473,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1469,\n                                                columnNumber: 15\n                                            }, this),\n                                            isLoadingKeysAndRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1481,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Loading API keys...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1482,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1480,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || error && error.startsWith(\"Error loading model configuration:\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1489,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1488,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-1\",\n                                                        children: \"No API Keys\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1491,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Add your first key using the form\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1492,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1487,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                                children: savedKeysWithRoles.map((key, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in\",\n                                                        style: {\n                                                            animationDelay: \"\".concat(index * 50, \"ms\")\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-sm font-semibold text-white truncate mr-2\",\n                                                                                    children: key.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1503,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1506,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        \"Default\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1505,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1502,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: [\n                                                                                                key.provider,\n                                                                                                \" (\",\n                                                                                                key.predefined_model_id,\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1514,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50\",\n                                                                                            children: [\n                                                                                                \"Temp: \",\n                                                                                                key.temperature\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1517,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1513,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                                    feature: \"custom_roles\",\n                                                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: \"Roles available on Starter plan+\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1526,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1525,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: key.assigned_roles.length > 0 ? key.assigned_roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50\",\n                                                                                                children: role.name\n                                                                                            }, role.id, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                                lineNumber: 1535,\n                                                                                                columnNumber: 37\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600\",\n                                                                                            children: \"No roles\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1540,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1532,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1522,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1512,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        !key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSetDefaultChatKey(key.id),\n                                                                            className: \"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Set as default chat model\",\n                                                                            children: \"Set Default\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1547,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1501,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 ml-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleEditKey(key),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Edit Model & Settings\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1566,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1559,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                            feature: \"custom_roles\",\n                                                                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                disabled: true,\n                                                                                className: \"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Role management requires Starter plan or higher\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1577,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1571,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setEditingRolesApiKey(key),\n                                                                                disabled: isDeletingKey === key.id,\n                                                                                className: \"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Manage Roles\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1588,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1581,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1568,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteKey(key.id, key.label),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Delete Key\",\n                                                                            children: isDeletingKey === key.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1599,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1601,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1591,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1558,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1500,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, key.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1499,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1497,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && error && !error.startsWith(\"Error loading model configuration:\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1614,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 font-medium text-sm\",\n                                                            children: [\n                                                                \"Could not load API keys/roles: \",\n                                                                error\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1615,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1613,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1612,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1468,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1467,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1300,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'user-api-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__.ApiKeyManager, {\n                                configId: configId,\n                                configName: configDetails.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1627,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1626,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1265,\n                    columnNumber: 9\n                }, this),\n                editingRolesApiKey && renderManageRolesModal(),\n                editingApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Edit API Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1644,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingApiKey(null),\n                                        className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1649,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1645,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1643,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-2\",\n                                                children: editingApiKey.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1655,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"Current: \",\n                                                    editingApiKey.provider,\n                                                    \" (\",\n                                                    editingApiKey.predefined_model_id,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1656,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1654,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Provider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1663,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300\",\n                                                        children: ((_llmProviders_find = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find((p)=>p.id === editingApiKey.provider)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.name) || editingApiKey.provider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1666,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: \"Provider cannot be changed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1669,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1662,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editModelId\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1673,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"editModelId\",\n                                                        value: editPredefinedModelId,\n                                                        onChange: (e)=>setEditPredefinedModelId(e.target.value),\n                                                        disabled: !editModelOptions.length,\n                                                        className: \"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30\",\n                                                        children: editModelOptions.length > 0 ? editModelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1685,\n                                                                columnNumber: 25\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            disabled: true,\n                                                            children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1690,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1676,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1672,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editTemperature\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: [\n                                                            \"Temperature: \",\n                                                            editTemperature\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1698,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        id: \"editTemperature\",\n                                                        min: \"0\",\n                                                        max: \"2\",\n                                                        step: \"0.1\",\n                                                        value: editTemperature,\n                                                        onChange: (e)=>setEditTemperature(parseFloat(e.target.value)),\n                                                        className: \"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1701,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"0.0 (Focused)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1712,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"1.0 (Balanced)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1713,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"2.0 (Creative)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1714,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1711,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1697,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1719,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1718,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1661,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1653,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setEditingApiKey(null),\n                                            className: \"btn-secondary\",\n                                            disabled: isSavingEdit,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1728,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSaveEdit,\n                                            disabled: isSavingEdit,\n                                            className: \"btn-primary\",\n                                            children: isSavingEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1742,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Saving...\"\n                                                ]\n                                            }, void 0, true) : 'Save Changes'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1735,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1727,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1726,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1642,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1641,\n                    columnNumber: 9\n                }, this),\n                !configDetails && !isLoadingConfig && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"h-8 w-8 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1758,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1757,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                            children: \"Model Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1760,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-8\",\n                            children: \"This API Model configuration could not be found or may have been deleted.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1761,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1766,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1762,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1756,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1773,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                    id: \"global-tooltip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1785,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1169,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigDetailsPage, \"iTFE/LOBiBHfFvkWnhfyoHl4n4c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe,\n        _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = ConfigDetailsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"ConfigDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/my-models/[configId]/page.tsx\n"));

/***/ })

});