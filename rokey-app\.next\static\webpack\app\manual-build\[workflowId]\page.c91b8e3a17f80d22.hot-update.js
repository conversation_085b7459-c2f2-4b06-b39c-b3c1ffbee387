"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/CentralRouterNode.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/manual-build/nodes/CentralRouterNode.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CentralRouterNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CentralRouterNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useNodes)();\n    const config = data.config;\n    // Get connected AI providers\n    const connectedProviders = edges.filter((edge)=>edge.target === id && edge.targetHandle === 'providers').map((edge)=>{\n        const sourceNode = nodes.find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'provider') {\n            const providerConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || 'Unknown Provider',\n                model: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || 'Unknown Model'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    // Get connected vision nodes\n    const connectedVision = edges.filter((edge)=>edge.target === id && edge.targetHandle === 'vision').map((edge)=>{\n        const sourceNode = nodes.find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'vision') {\n            const visionConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || 'Unknown Vision Provider',\n                model: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.modelId) || 'Unknown Model'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    // Get connected tools\n    const connectedTools = edges.filter((edge)=>edge.target === id && edge.targetHandle === 'tools').map((edge)=>{\n        const sourceNode = nodes.find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'tool') {\n            const toolConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) || 'Unknown Tool',\n                status: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.connectionStatus) || 'disconnected'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    // Check if classifier is connected\n    const hasClassifier = edges.some((edge)=>edge.target === id && edge.targetHandle === 'classifier' && nodes.find((node)=>node.id === edge.source && node.type === 'classifier'));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"providers\",\n                className: \"w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '20%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-blue-200 font-medium pointer-events-none\",\n                style: {\n                    left: -80,\n                    top: '15%'\n                },\n                children: \"AI Providers\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"vision\",\n                className: \"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '50%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-purple-200 font-medium pointer-events-none\",\n                style: {\n                    left: -80,\n                    top: '45%'\n                },\n                children: \"Vision Models\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"classifier\",\n                className: \"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '50%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-green-200 font-medium pointer-events-none\",\n                style: {\n                    left: -80,\n                    top: '45%'\n                },\n                children: \"Classifier\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"browsing\",\n                className: \"w-6 h-6 border-2 border-emerald-500 bg-emerald-700 hover:border-emerald-400 hover:bg-emerald-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '70%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-emerald-200 font-medium pointer-events-none\",\n                style: {\n                    left: -80,\n                    top: '65%'\n                },\n                children: \"Browsing\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"tools\",\n                className: \"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '75%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-green-200 font-medium pointer-events-none\",\n                style: {\n                    left: -50,\n                    top: '70%'\n                },\n                children: \"Tools\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"memory\",\n                className: \"w-6 h-6 border-2 border-yellow-500 bg-yellow-700 hover:border-yellow-400 hover:bg-yellow-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '90%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-yellow-200 font-medium pointer-events-none\",\n                style: {\n                    left: -60,\n                    top: '85%'\n                },\n                children: \"Memory\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                id: \"output\",\n                className: \"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors\",\n                style: {\n                    right: -12\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-orange-200 font-medium pointer-events-none\",\n                style: {\n                    right: -60,\n                    top: '45%'\n                },\n                children: \"Output\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[280px] rounded-lg border-2 transition-all duration-200 \".concat(data.hasError ? 'border-red-500 bg-red-900/20' : data.isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: data.hasError ? '#ef4444' : data.isConfigured ? '#ff6b35' : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: data.hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : 'linear-gradient(135deg, #ff6b3520, #ff6b3510)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: data.hasError ? '#ef444420' : '#ff6b3520',\n                                    color: data.hasError ? '#ef4444' : '#ff6b35'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: \"Central Router\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Smart routing hub for AI providers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    hasClassifier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                        title: \"Classifier Connected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    connectedProviders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-500 rounded-full\",\n                                        title: \"\".concat(connectedProviders.length, \" AI Providers\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this),\n                                    connectedVision.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full\",\n                                        title: \"\".concat(connectedVision.length, \" Vision Models\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    connectedTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-cyan-500 rounded-full\",\n                                        title: \"\".concat(connectedTools.length, \" Tools\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 space-y-3\",\n                        children: [\n                            connectedProviders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: [\n                                            \"AI Providers (\",\n                                            connectedProviders.length,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: connectedProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full border border-blue-700/30\",\n                                                children: provider.name\n                                            }, provider.id, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            connectedVision.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: [\n                                            \"Vision Models (\",\n                                            connectedVision.length,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: connectedVision.map((vision)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30\",\n                                                children: vision.name\n                                            }, vision.id, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            connectedTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: [\n                                            \"Tools (\",\n                                            connectedTools.length,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: connectedTools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs px-2 py-0.5 rounded-full border \".concat(tool.status === 'connected' ? 'bg-green-900/30 text-green-300 border-green-700/30' : 'bg-yellow-900/30 text-yellow-300 border-yellow-700/30'),\n                                                children: tool.name\n                                            }, tool.id, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            (config === null || config === void 0 ? void 0 : config.routingStrategy) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                                children: [\n                                    \"Strategy: \",\n                                    config.routingStrategy.replace('_', ' ').toUpperCase()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            !hasClassifier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                                children: \"⚠️ Connect classifier for smart routing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            connectedProviders.length === 0 && connectedVision.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-red-300 bg-red-900/20 px-2 py-1 rounded\",\n                                children: \"❌ No AI providers connected\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this),\n                            connectedTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded\",\n                                children: [\n                                    \"\\uD83D\\uDD27 \",\n                                    connectedTools.length,\n                                    \" tool\",\n                                    connectedTools.length > 1 ? 's' : '',\n                                    \" available\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            (connectedProviders.length > 0 || connectedVision.length > 0) && hasClassifier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded\",\n                                children: \"✅ Ready for smart routing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(CentralRouterNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useNodes\n    ];\n});\n_c = CentralRouterNode;\nvar _c;\n$RefreshReg$(_c, \"CentralRouterNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/CentralRouterNode.tsx\n"));

/***/ })

});