/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/providers/list-models/route";
exports.ids = ["app/api/providers/list-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproviders%2Flist-models%2Froute&page=%2Fapi%2Fproviders%2Flist-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproviders%2Flist-models%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproviders%2Flist-models%2Froute&page=%2Fapi%2Fproviders%2Flist-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproviders%2Flist-models%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_providers_list_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/providers/list-models/route.ts */ \"(rsc)/./src/app/api/providers/list-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/providers/list-models/route\",\n        pathname: \"/api/providers/list-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/providers/list-models/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\providers\\\\list-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_providers_list_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproviders%2Flist-models%2Froute&page=%2Fapi%2Fproviders%2Flist-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproviders%2Flist-models%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/providers/list-models/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/providers/list-models/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nasync function POST(request) {\n    try {\n        // Initialize Supabase client\n        // Ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY (or SUPABASE_SERVICE_ROLE_KEY for admin tasks)\n        // are set in your environment variables.\n        const supabaseUrl = \"https://hpkzzhpufhbxtxqaugjh.supabase.co\";\n        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\"; // Using anon key, RLS must allow reads\n        if (!supabaseUrl || !supabaseKey) {\n            console.error('Supabase URL or Key is not defined in environment variables.');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error: Supabase credentials missing.'\n            }, {\n                status: 500\n            });\n        }\n        const supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseKey);\n        const { data: modelsData, error: fetchError } = await supabase.from('models').select(`\n        id,\n        name,\n        display_name,\n        description,\n        provider_id,\n        family,\n        context_window,\n        input_token_limit,\n        output_token_limit,\n        modality,\n        is_public\n      `).order('provider_id', {\n            ascending: true\n        }).order('name', {\n            ascending: true\n        });\n        if (fetchError) {\n            console.error('Error fetching models from Supabase:', fetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to fetch models: ${fetchError.message}`\n            }, {\n                status: 500\n            });\n        }\n        if (!modelsData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                models: []\n            }, {\n                status: 200\n            });\n        }\n        // Transform the data if necessary to match the ModelInfo interface\n        const formattedModels = modelsData.map((model)=>({\n                id: model.id,\n                name: model.name,\n                display_name: model.display_name || model.name,\n                description: model.description,\n                provider_id: model.provider_id,\n                family: model.family,\n                context_window: model.context_window,\n                input_token_limit: model.input_token_limit,\n                output_token_limit: model.output_token_limit,\n                modality: model.modality || 'unknown',\n                is_public: model.is_public\n            }));\n        // Apply the same filtering logic we had before if desired\n        // For now, returning all models that were inserted.\n        // You might want to re-introduce modality filters here based on your requirements.\n        const allowedModalities = [\n            'text',\n            'multimodal',\n            'image'\n        ]; // Example filter\n        const filteredModels = formattedModels.filter((model)=>model.modality && allowedModalities.includes(model.modality));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            models: filteredModels\n        }, {\n            status: 200\n        });\n    } catch (err) {\n        console.error('Unexpected error in /api/providers/list-models:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `An unexpected error occurred: ${err.message || 'Unknown error'}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/providers/list-models/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproviders%2Flist-models%2Froute&page=%2Fapi%2Fproviders%2Flist-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproviders%2Flist-models%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();