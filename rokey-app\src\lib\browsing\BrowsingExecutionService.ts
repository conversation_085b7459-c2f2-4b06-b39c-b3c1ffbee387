// Browsing Execution Service - Handles browsing model selection with fallback support
// Integrates with BrowserlessService for actual web browsing

import BrowserlessService from '@/lib/browserless';
import { BrowsingModel } from '@/types/customApiConfigs';
import { SmartBrowsingExecutor } from './SmartBrowsingExecutor';

interface BrowsingExecutionResult {
  success: boolean;
  content?: string;
  error?: string;
  modelUsed?: string;
  providerUsed?: string;
  browsingData?: any;
}

interface BrowsingConfig {
  browsing_enabled: boolean;
  browsing_models: BrowsingModel[];
}

export class BrowsingExecutionService {
  private static instance: BrowsingExecutionService;
  private browserlessService: BrowserlessService;
  private smartBrowsingExecutor: SmartBrowsingExecutor;

  constructor() {
    this.browserlessService = BrowserlessService.getInstance();
    this.smartBrowsingExecutor = SmartBrowsingExecutor.getInstance();
  }

  static getInstance(): BrowsingExecutionService {
    if (!BrowsingExecutionService.instance) {
      BrowsingExecutionService.instance = new BrowsingExecutionService();
    }
    return BrowsingExecutionService.instance;
  }

  /**
   * Execute browsing with model fallback support
   * Now uses SmartBrowsingExecutor for intelligent, plan-based browsing
   */
  async executeBrowsing(
    query: string,
    browsingConfig: BrowsingConfig,
    browsingType: 'search' | 'navigate' | 'extract' = 'search',
    refinedQuery?: string,
    useSmartBrowsing: boolean = true
  ): Promise<BrowsingExecutionResult> {
    try {
      if (!browsingConfig.browsing_enabled) {
        return {
          success: false,
          error: 'Browsing is not enabled for this configuration'
        };
      }

      if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {
        return {
          success: false,
          error: 'No browsing models configured'
        };
      }

      console.log(`[Browsing Execution] Starting ${useSmartBrowsing ? 'SMART' : 'SIMPLE'} browsing`);
      console.log(`[Browsing Execution] Query: "${query}", Type: ${browsingType}`);

      // Use Smart Browsing for complex tasks
      if (useSmartBrowsing) {
        console.log(`[Browsing Execution] 🧠 Using Smart Browsing Executor`);

        const smartResult = await this.smartBrowsingExecutor.executeSmartBrowsing(
          refinedQuery || query,
          browsingConfig,
          browsingType
        );

        if (smartResult.success) {
          return {
            success: true,
            content: smartResult.content,
            modelUsed: browsingConfig.browsing_models[0]?.model || 'smart-browsing',
            providerUsed: browsingConfig.browsing_models[0]?.provider || 'smart-browsing',
            browsingData: smartResult.plan
          };
        } else {
          console.log(`[Browsing Execution] Smart browsing failed, falling back to simple browsing: ${smartResult.error}`);
          // Fall back to simple browsing
        }
      }

      // Fallback to simple browsing (original logic)
      console.log(`[Browsing Execution] 🔄 Using Simple Browsing (fallback)`);
      return await this.executeSimpleBrowsing(query, browsingConfig, browsingType, refinedQuery);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[Browsing Execution] Fatal error:', errorMessage);
      return {
        success: false,
        error: `Browsing execution failed: ${errorMessage}`
      };
    }
  }

  /**
   * Execute simple browsing (original logic) as fallback
   */
  private async executeSimpleBrowsing(
    query: string,
    browsingConfig: BrowsingConfig,
    browsingType: 'search' | 'navigate' | 'extract' = 'search',
    refinedQuery?: string
  ): Promise<BrowsingExecutionResult> {
    // Sort models by order for fallback
    const sortedModels = [...browsingConfig.browsing_models].sort((a, b) => a.order - b.order);

    console.log(`[Simple Browsing] Starting with ${sortedModels.length} models configured`);

    let lastError: string | null = null;

    // Try each model in order (strict fallback pattern)
    for (const model of sortedModels) {
      try {
        console.log(`[Simple Browsing] Attempting with ${model.provider}/${model.model}`);

        // First, perform the web browsing
        const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);

        if (!browsingResult.success) {
          throw new Error(browsingResult.error || 'Browsing failed');
        }

        console.log(`[Simple Browsing] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);

        // Then, use the AI model to process the browsing results
        const aiResult = await this.processWithAI(
          query,
          browsingResult.data,
          model,
          browsingType
        );

        if (aiResult.success) {
          console.log(`[Simple Browsing] ✅ Success with ${model.provider}/${model.model}`);
          return {
            success: true,
            content: aiResult.content,
            modelUsed: model.model,
            providerUsed: model.provider,
            browsingData: browsingResult.data
          };
        } else {
          throw new Error(aiResult.error || 'AI processing failed');
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        lastError = errorMessage;
        console.log(`[Simple Browsing] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);

        // Continue to next model in fallback chain
        continue;
      }
    }

    // If we get here, all models failed
    return {
      success: false,
      error: `All browsing models failed. Last error: ${lastError}`
    };
  }

  /**
   * Perform web browsing using BrowserlessService
   */
  private async performWebBrowsing(
    query: string,
    browsingType: 'search' | 'navigate' | 'extract'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      let result;

      switch (browsingType) {
        case 'search':
          // Use search functionality
          result = await this.browserlessService.searchAndExtract(query);
          break;
          
        case 'navigate':
          // Try to extract URL from query or use as-is
          const urlMatch = query.match(/https?:\/\/[^\s]+/);
          const url = urlMatch ? urlMatch[0] : query;
          result = await this.browserlessService.navigateAndExtract(url);
          break;
          
        case 'extract':
          // Similar to navigate but with specific extraction
          const extractUrl = query.match(/https?:\/\/[^\s]+/)?.[0] || query;
          result = await this.browserlessService.navigateAndExtract(extractUrl);
          break;
          
        default:
          // Default to search
          result = await this.browserlessService.searchAndExtract(query);
      }

      if (result && result.data) {
        console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);
        return { success: true, data: result.data };
      } else {
        return { success: false, error: 'No data returned from browsing' };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[Browsing Execution] Web browsing failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Process browsing results with AI model
   */
  private async processWithAI(
    originalQuery: string,
    browsingData: any,
    model: BrowsingModel,
    browsingType: string
  ): Promise<{ success: boolean; content?: string; error?: string }> {
    try {
      const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);
      
      // Call the appropriate AI provider
      const response = await this.callAIProvider(prompt, model);
      
      if (response && response.content) {
        return { success: true, content: response.content };
      } else {
        return { success: false, error: 'No content returned from AI model' };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Build processing prompt for AI model
   */
  private buildProcessingPrompt(originalQuery: string, browsingData: any, browsingType: string): string {
    const dataStr = JSON.stringify(browsingData, null, 2);
    
    return `You are an AI assistant that processes web browsing results to answer user queries.

USER QUERY: "${originalQuery}"
BROWSING TYPE: ${browsingType}

WEB BROWSING RESULTS:
${dataStr}

INSTRUCTIONS:
1. Analyze the browsing results carefully
2. Extract relevant information that answers the user's query
3. Provide a comprehensive, well-structured response
4. If the browsing results don't contain relevant information, say so clearly
5. Include specific details, numbers, dates, and facts from the browsing results
6. Organize the information in a clear, readable format
7. Cite sources when possible (URLs, website names, etc.)

Please provide a helpful response based on the browsing results:`;
  }

  /**
   * Get the correct model ID for API calls (following RouKey's pattern)
   * OpenRouter keeps full model ID, other providers strip the prefix
   */
  private getEffectiveModelId(model: BrowsingModel): string {
    // For OpenRouter, return the full model ID
    if (model.provider.toLowerCase() === 'openrouter') {
      return model.model;
    }

    // For other providers, extract the model name after the prefix
    const parts = model.model.split('/');
    return parts.length > 1 ? parts[parts.length - 1] : model.model;
  }

  /**
   * Call AI provider based on model configuration
   */
  private async callAIProvider(
    prompt: string,
    model: BrowsingModel
  ): Promise<{ content?: string; error?: string }> {
    try {
      const messages = [
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      // Get the effective model ID following RouKey's pattern
      const effectiveModelId = this.getEffectiveModelId(model);

      let apiUrl: string;
      let headers: Record<string, string>;
      let body: any;

      // Configure API call based on provider
      switch (model.provider) {
        case 'openai':
          apiUrl = 'https://api.openai.com/v1/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`
          };
          body = {
            model: effectiveModelId,
            messages,
            temperature: model.temperature || 0.2,
            max_tokens: 2000
          };
          break;

        case 'anthropic':
          apiUrl = 'https://api.anthropic.com/v1/messages';
          headers = {
            'Content-Type': 'application/json',
            'x-api-key': model.api_key,
            'anthropic-version': '2023-06-01'
          };
          body = {
            model: effectiveModelId,
            messages,
            temperature: model.temperature || 0.2,
            max_tokens: 2000
          };
          break;

        case 'google':
          apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`
          };
          body = {
            model: effectiveModelId,
            messages,
            temperature: model.temperature || 0.2,
            max_tokens: 2000
          };
          break;

        case 'openrouter':
          apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`,
            'HTTP-Referer': 'https://roukey.online',
            'X-Title': 'RouKey'
          };
          body = {
            model: effectiveModelId, // OpenRouter keeps the full model ID
            messages,
            temperature: model.temperature || 0.2,
            max_tokens: 2000
          };
          break;

        default:
          throw new Error(`Unsupported provider: ${model.provider}`);
      }

      console.log(`[Browsing AI] Calling ${model.provider} API with model ${effectiveModelId} (original: ${model.model})...`);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: AbortSignal.timeout(30000) // 30s timeout
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);
        throw new Error(`API error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));

      // Extract content based on provider response format
      let content: string | undefined;

      if (model.provider === 'anthropic') {
        content = result.content?.[0]?.text;
      } else {
        // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)
        content = result.choices?.[0]?.message?.content;
      }

      console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);

      if (!content || content.trim().length === 0) {
        console.error(`[Browsing AI] No content extracted from response. Full response:`, result);
        return { error: 'No content returned from AI model - empty response' };
      }

      return { content };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);
      return { error: errorMessage };
    }
  }
}

export default BrowsingExecutionService;
