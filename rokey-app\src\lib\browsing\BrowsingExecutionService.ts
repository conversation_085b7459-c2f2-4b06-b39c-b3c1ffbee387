// Browsing Execution Service - Handles browsing model selection with fallback support
// Integrates with BrowserlessService for actual web browsing

import BrowserlessService from '@/lib/browserless';
import { BrowsingModel } from '@/types/customApiConfigs';

interface BrowsingExecutionResult {
  success: boolean;
  content?: string;
  error?: string;
  modelUsed?: string;
  providerUsed?: string;
  browsingData?: any;
}

interface BrowsingConfig {
  browsing_enabled: boolean;
  browsing_models: BrowsingModel[];
}

export class BrowsingExecutionService {
  private static instance: BrowsingExecutionService;
  private browserlessService: BrowserlessService;

  constructor() {
    this.browserlessService = BrowserlessService.getInstance();
  }

  static getInstance(): BrowsingExecutionService {
    if (!BrowsingExecutionService.instance) {
      BrowsingExecutionService.instance = new BrowsingExecutionService();
    }
    return BrowsingExecutionService.instance;
  }

  /**
   * Execute browsing with model fallback support
   */
  async executeBrowsing(
    query: string,
    browsingConfig: BrowsingConfig,
    browsingType: 'search' | 'navigate' | 'extract' = 'search',
    refinedQuery?: string
  ): Promise<BrowsingExecutionResult> {
    try {
      if (!browsingConfig.browsing_enabled) {
        return {
          success: false,
          error: 'Browsing is not enabled for this configuration'
        };
      }

      if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {
        return {
          success: false,
          error: 'No browsing models configured'
        };
      }

      // Sort models by order for fallback
      const sortedModels = [...browsingConfig.browsing_models].sort((a, b) => a.order - b.order);
      
      console.log(`[Browsing Execution] Starting browsing with ${sortedModels.length} models configured`);
      console.log(`[Browsing Execution] Query: "${query}", Type: ${browsingType}`);

      let lastError: string | null = null;

      // Try each model in order (strict fallback pattern)
      for (const model of sortedModels) {
        try {
          console.log(`[Browsing Execution] Attempting with ${model.provider}/${model.model}`);
          
          // First, perform the web browsing
          const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);
          
          if (!browsingResult.success) {
            throw new Error(browsingResult.error || 'Browsing failed');
          }

          // Then, use the AI model to process the browsing results
          const aiResult = await this.processWithAI(
            query,
            browsingResult.data,
            model,
            browsingType
          );

          if (aiResult.success) {
            console.log(`[Browsing Execution] ✅ Success with ${model.provider}/${model.model}`);
            return {
              success: true,
              content: aiResult.content,
              modelUsed: model.model,
              providerUsed: model.provider,
              browsingData: browsingResult.data
            };
          } else {
            throw new Error(aiResult.error || 'AI processing failed');
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          lastError = errorMessage;
          
          console.warn(`[Browsing Execution] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);
          
          // Continue to next model in fallback chain
          continue;
        }
      }

      // All models failed
      return {
        success: false,
        error: `All browsing models failed. Last error: ${lastError}`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[Browsing Execution] Fatal error:', errorMessage);
      return {
        success: false,
        error: `Browsing execution failed: ${errorMessage}`
      };
    }
  }

  /**
   * Perform web browsing using BrowserlessService
   */
  private async performWebBrowsing(
    query: string,
    browsingType: 'search' | 'navigate' | 'extract'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      let result;

      switch (browsingType) {
        case 'search':
          // Use search functionality
          result = await this.browserlessService.searchAndExtract(query);
          break;
          
        case 'navigate':
          // Try to extract URL from query or use as-is
          const urlMatch = query.match(/https?:\/\/[^\s]+/);
          const url = urlMatch ? urlMatch[0] : query;
          result = await this.browserlessService.navigateAndExtract(url);
          break;
          
        case 'extract':
          // Similar to navigate but with specific extraction
          const extractUrl = query.match(/https?:\/\/[^\s]+/)?.[0] || query;
          result = await this.browserlessService.navigateAndExtract(extractUrl);
          break;
          
        default:
          // Default to search
          result = await this.browserlessService.searchAndExtract(query);
      }

      if (result && result.data) {
        console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);
        return { success: true, data: result.data };
      } else {
        return { success: false, error: 'No data returned from browsing' };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[Browsing Execution] Web browsing failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Process browsing results with AI model
   */
  private async processWithAI(
    originalQuery: string,
    browsingData: any,
    model: BrowsingModel,
    browsingType: string
  ): Promise<{ success: boolean; content?: string; error?: string }> {
    try {
      const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);
      
      // Call the appropriate AI provider
      const response = await this.callAIProvider(prompt, model);
      
      if (response && response.content) {
        return { success: true, content: response.content };
      } else {
        return { success: false, error: 'No content returned from AI model' };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Build processing prompt for AI model
   */
  private buildProcessingPrompt(originalQuery: string, browsingData: any, browsingType: string): string {
    const dataStr = JSON.stringify(browsingData, null, 2);
    
    return `You are an AI assistant that processes web browsing results to answer user queries.

USER QUERY: "${originalQuery}"
BROWSING TYPE: ${browsingType}

WEB BROWSING RESULTS:
${dataStr}

INSTRUCTIONS:
1. Analyze the browsing results carefully
2. Extract relevant information that answers the user's query
3. Provide a comprehensive, well-structured response
4. If the browsing results don't contain relevant information, say so clearly
5. Include specific details, numbers, dates, and facts from the browsing results
6. Organize the information in a clear, readable format
7. Cite sources when possible (URLs, website names, etc.)

Please provide a helpful response based on the browsing results:`;
  }

  /**
   * Call AI provider based on model configuration
   */
  private async callAIProvider(
    prompt: string,
    model: BrowsingModel
  ): Promise<{ content?: string; error?: string }> {
    try {
      const messages = [
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      let apiUrl: string;
      let headers: Record<string, string>;
      let body: any;

      // Configure API call based on provider
      switch (model.provider) {
        case 'openai':
          apiUrl = 'https://api.openai.com/v1/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`
          };
          body = {
            model: model.model,
            messages,
            temperature: model.temperature || 0.2,
            max_tokens: 2000
          };
          break;

        case 'anthropic':
          apiUrl = 'https://api.anthropic.com/v1/messages';
          headers = {
            'Content-Type': 'application/json',
            'x-api-key': model.api_key,
            'anthropic-version': '2023-06-01'
          };
          body = {
            model: model.model,
            messages,
            temperature: model.temperature || 0.2,
            max_tokens: 2000
          };
          break;

        case 'google':
          apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`
          };
          body = {
            model: model.model,
            messages,
            temperature: model.temperature || 0.2,
            max_tokens: 2000
          };
          break;

        default:
          throw new Error(`Unsupported provider: ${model.provider}`);
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: AbortSignal.timeout(30000) // 30s timeout
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      
      // Extract content based on provider response format
      let content: string | undefined;
      
      if (model.provider === 'anthropic') {
        content = result.content?.[0]?.text;
      } else {
        content = result.choices?.[0]?.message?.content;
      }

      return { content };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { error: errorMessage };
    }
  }
}

export default BrowsingExecutionService;
