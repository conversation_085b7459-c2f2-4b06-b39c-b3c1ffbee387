// Browserless.io API service with key rotation
// Similar to Jina API key rotation system

interface BrowserlessResponse {
  data: any;
  type: string;
  error?: string;
}

interface BrowserlessConfig {
  timeout?: number;
  viewport?: {
    width: number;
    height: number;
  };
  userAgent?: string;
}

class BrowserlessService {
  private static instance: BrowserlessService;
  private apiKeys: string[] = [];
  private currentKeyIndex = 0;
  private keyUsageCount: Map<string, number> = new Map();
  private keyErrors: Map<string, number> = new Map();
  private readonly MAX_RETRIES = 3;
  private readonly ERROR_THRESHOLD = 5;
  private readonly ENDPOINT = 'https://production-sfo.browserless.io';

  constructor() {
    this.initializeKeys();
  }

  static getInstance(): BrowserlessService {
    if (!BrowserlessService.instance) {
      BrowserlessService.instance = new BrowserlessService();
    }
    return BrowserlessService.instance;
  }

  private initializeKeys(): void {
    const keysString = process.env.BROWSERLESS_API_KEYS;
    if (!keysString) {
      console.error('BROWSERLESS_API_KEYS not found in environment variables');
      return;
    }

    this.apiKeys = keysString.split(',').map(key => key.trim()).filter(Boolean);
    console.log(`Initialized Browserless service with ${this.apiKeys.length} API keys`);

    // Initialize usage tracking
    this.apiKeys.forEach(key => {
      this.keyUsageCount.set(key, 0);
      this.keyErrors.set(key, 0);
    });
  }

  private getNextApiKey(): string {
    if (this.apiKeys.length === 0) {
      throw new Error('No Browserless API keys available');
    }

    // Find the key with lowest usage and errors
    let bestKey = this.apiKeys[0];
    let bestScore = this.calculateKeyScore(bestKey);

    for (const key of this.apiKeys) {
      const score = this.calculateKeyScore(key);
      if (score < bestScore) {
        bestKey = key;
        bestScore = score;
      }
    }

    return bestKey;
  }

  private calculateKeyScore(key: string): number {
    const usage = this.keyUsageCount.get(key) || 0;
    const errors = this.keyErrors.get(key) || 0;
    // Higher score = worse key (more usage + more errors)
    return usage + (errors * 10);
  }

  private incrementKeyUsage(key: string): void {
    const currentUsage = this.keyUsageCount.get(key) || 0;
    this.keyUsageCount.set(key, currentUsage + 1);
  }

  private incrementKeyError(key: string): void {
    const currentErrors = this.keyErrors.get(key) || 0;
    this.keyErrors.set(key, currentErrors + 1);
  }

  private isKeyHealthy(key: string): boolean {
    const errors = this.keyErrors.get(key) || 0;
    return errors < this.ERROR_THRESHOLD;
  }

  private getHealthyKeys(): string[] {
    return this.apiKeys.filter(key => this.isKeyHealthy(key));
  }

  async executeFunction(
    code: string,
    context?: any,
    config?: BrowserlessConfig
  ): Promise<BrowserlessResponse> {
    const healthyKeys = this.getHealthyKeys();
    
    if (healthyKeys.length === 0) {
      // Reset error counts if all keys are unhealthy
      this.keyErrors.clear();
      this.apiKeys.forEach(key => this.keyErrors.set(key, 0));
      console.log('All Browserless keys were unhealthy, resetting error counts');
    }

    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.MAX_RETRIES; attempt++) {
      try {
        const apiKey = this.getNextApiKey();
        this.incrementKeyUsage(apiKey);

        const response = await this.makeRequest(apiKey, code, context, config);
        
        // Success - return the response
        return response;
      } catch (error) {
        lastError = error as Error;
        console.error(`Browserless attempt ${attempt + 1} failed:`, error);
        
        // If it's a rate limit or quota error, mark the key as having an error
        if (this.isRateLimitError(error as Error)) {
          const currentKey = this.getNextApiKey();
          this.incrementKeyError(currentKey);
        }
      }
    }

    throw lastError || new Error('All Browserless API attempts failed');
  }

  private async makeRequest(
    apiKey: string,
    code: string,
    context?: any,
    config?: BrowserlessConfig
  ): Promise<BrowserlessResponse> {
    const url = `${this.ENDPOINT}/function?token=${apiKey}`;
    
    const requestBody = context ? {
      code,
      context
    } : code;

    const headers = {
      'Content-Type': context ? 'application/json' : 'application/javascript',
      'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'
    };

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: context ? JSON.stringify(requestBody) : code,
      signal: AbortSignal.timeout(config?.timeout || 30000)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Browserless API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    return result;
  }

  private isRateLimitError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('rate limit') || 
           message.includes('quota') || 
           message.includes('429') ||
           message.includes('too many requests');
  }

  // Convenience methods for common browser tasks
  async navigateAndExtract(url: string, selector?: string): Promise<any> {
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });
        
        const title = await page.title();
        const content = ${selector ? 
          `await page.$eval("${selector}", el => el.textContent || el.innerText)` : 
          'await page.evaluate(() => document.body.innerText)'
        };
        
        return {
          data: {
            url: "${url}",
            title,
            content: content?.trim() || ""
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  async searchAndExtract(query: string, searchEngine: string = 'google'): Promise<any> {
    const searchUrl = searchEngine === 'google'
      ? `https://www.google.com/search?q=${encodeURIComponent(query)}`
      : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;

    const code = `
      export default async function ({ page }) {
        await page.goto("${searchUrl}", { waitUntil: 'networkidle0' });

        // Wait for search results to load with multiple fallback selectors
        let resultsLoaded = false;
        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];
        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];

        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;

        for (const selector of selectorsToTry) {
          try {
            await page.waitForSelector(selector, { timeout: 3000 });
            resultsLoaded = true;
            console.log('Found results with selector:', selector);
            break;
          } catch (e) {
            console.log('Selector failed:', selector);
            continue;
          }
        }

        if (!resultsLoaded) {
          // Give it one more chance with a longer timeout on the most common selector
          try {
            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });
          } catch (e) {
            console.log('All selectors failed, proceeding anyway...');
          }
        }

        const results = await page.evaluate(() => {
          // Try multiple selectors for extracting results
          const googleSelectors = [
            '[data-ved] h3',
            'h3',
            '.g h3',
            '.LC20lb',
            '.DKV0Md',
            '#search h3',
            '.yuRUbf h3'
          ];
          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];

          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;
          let elements = [];

          for (const selector of selectorsToTry) {
            elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
              console.log('Found', elements.length, 'results with selector:', selector);
              break;
            }
          }

          return Array.from(elements).slice(0, 5).map(el => ({
            title: el.textContent?.trim() || '',
            link: el.closest('a')?.href || el.href || ''
          })).filter(item => item.title && item.link);
        });

        return {
          data: {
            query: "${query}",
            searchEngine: "${searchEngine}",
            results,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  async takeScreenshot(url: string, options?: {
    fullPage?: boolean;
    selector?: string;
    quality?: number;
  }): Promise<any> {
    const fullPage = options?.fullPage ?? false;
    const selector = options?.selector || '';
    const quality = options?.quality || 80;

    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        let screenshot;
        if ("${selector}") {
          // Screenshot specific element
          const element = await page.waitForSelector("${selector}", { timeout: 10000 });
          screenshot = await element.screenshot({
            encoding: 'base64',
            type: 'png'
          });
        } else {
          // Screenshot full page or viewport
          screenshot = await page.screenshot({
            encoding: 'base64',
            fullPage: ${fullPage},
            type: 'png',
            quality: ${quality}
          });
        }

        return {
          data: {
            url: "${url}",
            screenshot: screenshot,
            selector: "${selector}",
            fullPage: ${fullPage},
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * Advanced form filling with intelligent field detection
   */
  async fillForm(url: string, formData: Record<string, any>, options?: {
    submitAfterFill?: boolean;
    waitForNavigation?: boolean;
    formSelector?: string;
  }): Promise<any> {
    const submitAfterFill = options?.submitAfterFill ?? false;
    const waitForNavigation = options?.waitForNavigation ?? false;
    const formSelector = options?.formSelector || 'form';

    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        const formData = ${JSON.stringify(formData)};
        const results = [];

        // Wait for form to be present
        await page.waitForSelector("${formSelector}", { timeout: 10000 });

        // Fill each field intelligently
        for (const [fieldName, value] of Object.entries(formData)) {
          try {
            // Try multiple selector strategies
            const selectors = [
              \`input[name="\${fieldName}"]\`,
              \`input[id="\${fieldName}"]\`,
              \`textarea[name="\${fieldName}"]\`,
              \`select[name="\${fieldName}"]\`,
              \`input[placeholder*="\${fieldName}"]\`,
              \`input[aria-label*="\${fieldName}"]\`,
              \`[data-testid="\${fieldName}"]\`
            ];

            let filled = false;
            for (const selector of selectors) {
              const elements = await page.$$(selector);
              if (elements.length > 0) {
                const element = elements[0];
                const tagName = await element.evaluate(el => el.tagName.toLowerCase());

                if (tagName === 'select') {
                  await element.selectOption(value.toString());
                } else if (tagName === 'input') {
                  const inputType = await element.getAttribute('type');
                  if (inputType === 'checkbox' || inputType === 'radio') {
                    if (value) await element.check();
                  } else {
                    await element.fill(value.toString());
                  }
                } else {
                  await element.fill(value.toString());
                }

                results.push({
                  field: fieldName,
                  selector: selector,
                  value: value,
                  success: true
                });
                filled = true;
                break;
              }
            }

            if (!filled) {
              results.push({
                field: fieldName,
                value: value,
                success: false,
                error: 'Field not found'
              });
            }
          } catch (error) {
            results.push({
              field: fieldName,
              value: value,
              success: false,
              error: error.message
            });
          }
        }

        let submitResult = null;
        if (${submitAfterFill}) {
          try {
            const submitButton = await page.$('input[type="submit"], button[type="submit"], button:has-text("Submit")');
            if (submitButton) {
              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}
              submitResult = { success: true, message: 'Form submitted successfully' };
            } else {
              submitResult = { success: false, error: 'Submit button not found' };
            }
          } catch (error) {
            submitResult = { success: false, error: error.message };
          }
        }

        return {
          data: {
            url: "${url}",
            formFillResults: results,
            submitResult: submitResult,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * CAPTCHA solving with multiple strategies
   */
  async solveCaptcha(url: string, captchaType: 'recaptcha' | 'hcaptcha' | 'text' = 'recaptcha'): Promise<any> {
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        const captchaType = "${captchaType}";
        let result = { success: false, type: captchaType };

        try {
          if (captchaType === 'recaptcha') {
            // Look for reCAPTCHA
            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');
            if (recaptcha) {
              // For now, we'll detect and report the presence
              // In production, integrate with 2captcha or similar service
              const sitekey = await recaptcha.getAttribute('data-sitekey');
              result = {
                success: false,
                type: 'recaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'reCAPTCHA detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'hcaptcha') {
            // Look for hCaptcha
            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');
            if (hcaptcha) {
              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');
              result = {
                success: false,
                type: 'hcaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'hCaptcha detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'text') {
            // Look for text-based CAPTCHA
            const textCaptcha = await page.$('img[src*="captcha"], img[alt*="captcha"], .captcha-image');
            if (textCaptcha) {
              result = {
                success: false,
                type: 'text',
                detected: true,
                message: 'Text CAPTCHA detected but solving not implemented yet'
              };
            }
          }

          // If no CAPTCHA detected
          if (!result.detected) {
            result = {
              success: true,
              type: captchaType,
              detected: false,
              message: 'No CAPTCHA detected on page'
            };
          }
        } catch (error) {
          result = {
            success: false,
            type: captchaType,
            error: error.message
          };
        }

        return {
          data: {
            url: "${url}",
            captchaResult: result,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * Execute custom JavaScript with advanced capabilities
   */
  async executeAdvancedScript(url: string, script: string, options?: {
    waitForSelector?: string;
    timeout?: number;
    returnType?: 'json' | 'text' | 'screenshot';
  }): Promise<any> {
    const waitForSelector = options?.waitForSelector || '';
    const timeout = options?.timeout || 30000;
    const returnType = options?.returnType || 'json';

    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        ${waitForSelector ? `await page.waitForSelector("${waitForSelector}", { timeout: ${timeout} });` : ''}

        // Execute custom script
        const scriptResult = await page.evaluate(() => {
          ${script}
        });

        let finalResult = scriptResult;

        if ("${returnType}" === 'screenshot') {
          const screenshot = await page.screenshot({
            encoding: 'base64',
            type: 'png'
          });
          finalResult = {
            scriptResult: scriptResult,
            screenshot: screenshot
          };
        }

        return {
          data: {
            url: "${url}",
            result: finalResult,
            returnType: "${returnType}",
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * Smart content extraction with multiple strategies
   */
  async smartExtract(url: string, extractionGoals: string[]): Promise<any> {
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        const goals = ${JSON.stringify(extractionGoals)};
        const results = {};

        // Common extraction patterns
        const extractors = {
          prices: () => {
            const priceSelectors = [
              '[class*="price"]', '[id*="price"]', '.cost', '.amount',
              '[data-testid*="price"]', '.currency', '[class*="dollar"]'
            ];
            const prices = [];
            priceSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const text = el.textContent?.trim();
                if (text && /[$£€¥₹]|\\d+\\.\\d{2}/.test(text)) {
                  prices.push({
                    text: text,
                    selector: selector,
                    element: el.tagName
                  });
                }
              });
            });
            return prices;
          },

          contact: () => {
            const contactSelectors = [
              '[href^="mailto:"]', '[href^="tel:"]', '.contact', '.email', '.phone'
            ];
            const contacts = [];
            contactSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                contacts.push({
                  text: el.textContent?.trim(),
                  href: el.getAttribute('href'),
                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'
                });
              });
            });
            return contacts;
          },

          products: () => {
            const productSelectors = [
              '.product', '[class*="product"]', '.item', '[data-testid*="product"]'
            ];
            const products = [];
            productSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const title = el.querySelector('h1, h2, h3, .title, [class*="title"]')?.textContent?.trim();
                const price = el.querySelector('[class*="price"], .cost')?.textContent?.trim();
                const image = el.querySelector('img')?.src;
                if (title) {
                  products.push({ title, price, image });
                }
              });
            });
            return products;
          },

          text: () => {
            // Extract main content
            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];
            let content = '';
            for (const selector of contentSelectors) {
              const el = document.querySelector(selector);
              if (el) {
                content = el.textContent?.trim() || '';
                break;
              }
            }
            if (!content) {
              content = document.body.textContent?.trim() || '';
            }
            return content.substring(0, 5000); // Limit to 5000 chars
          },

          links: () => {
            const links = [];
            document.querySelectorAll('a[href]').forEach(el => {
              const href = el.getAttribute('href');
              const text = el.textContent?.trim();
              if (href && text && !href.startsWith('#')) {
                links.push({
                  url: new URL(href, window.location.href).href,
                  text: text
                });
              }
            });
            return links.slice(0, 50); // Limit to 50 links
          }
        };

        // Execute extractors based on goals
        goals.forEach(goal => {
          const goalLower = goal.toLowerCase();
          if (goalLower.includes('price') || goalLower.includes('cost')) {
            results.prices = extractors.prices();
          }
          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {
            results.contact = extractors.contact();
          }
          if (goalLower.includes('product') || goalLower.includes('item')) {
            results.products = extractors.products();
          }
          if (goalLower.includes('text') || goalLower.includes('content')) {
            results.text = extractors.text();
          }
          if (goalLower.includes('link') || goalLower.includes('url')) {
            results.links = extractors.links();
          }
        });

        // If no specific goals, extract everything
        if (goals.length === 0) {
          Object.keys(extractors).forEach(key => {
            results[key] = extractors[key]();
          });
        }

        return {
          data: {
            url: "${url}",
            extractionGoals: goals,
            results: results,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  // Get service statistics
  getStats(): any {
    return {
      totalKeys: this.apiKeys.length,
      healthyKeys: this.getHealthyKeys().length,
      keyUsage: Object.fromEntries(this.keyUsageCount),
      keyErrors: Object.fromEntries(this.keyErrors)
    };
  }
}

export default BrowserlessService;
