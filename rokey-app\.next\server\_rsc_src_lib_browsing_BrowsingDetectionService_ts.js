"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingDetectionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingDetectionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browsing/BrowsingDetectionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingDetectionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingDetectionService: () => (/* binding */ BrowsingDetectionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browsing Detection Service - Determines when user queries require web browsing\n// Similar to RAG detection but for real-time web information needs\nclass BrowsingDetectionService {\n    constructor(){\n        this.classificationApiKey = null;\n        this.classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY || null;\n    }\n    static getInstance() {\n        if (!BrowsingDetectionService.instance) {\n            BrowsingDetectionService.instance = new BrowsingDetectionService();\n        }\n        return BrowsingDetectionService.instance;\n    }\n    /**\n   * Determine if a user query requires web browsing for real-time information\n   */ async shouldTriggerBrowsing(query, conversationContext) {\n        try {\n            // Skip very short queries immediately\n            if (query.trim().length < 8) {\n                return {\n                    shouldBrowse: false\n                };\n            }\n            if (!this.classificationApiKey) {\n                console.warn('[Browsing Detection] No classification API key, using fallback logic');\n                return this.fallbackDetection(query);\n            }\n            const classificationPrompt = `You are an intelligent browsing classifier. Analyze if this user query requires real-time web browsing to get current information.\n\nUSER QUERY: \"${query}\"\n\n${conversationContext ? `CONVERSATION CONTEXT: ${conversationContext}` : ''}\n\nCLASSIFICATION CRITERIA:\n✅ REQUIRES BROWSING (YES) when:\n- Query asks for CURRENT/RECENT information (news, events, prices, weather, etc.)\n- Query mentions specific dates, \"today\", \"now\", \"latest\", \"current\", \"recent\"\n- Query asks about live data (stock prices, exchange rates, sports scores)\n- Query asks about current events, breaking news, or recent developments\n- Query asks about website content, specific URLs, or online resources\n- Query asks \"what's happening\", \"latest news about\", \"current status of\"\n- Query asks about real-time information that changes frequently\n- Query asks about specific companies' current status, recent announcements\n- Query asks about weather, traffic, or location-based current information\n- Query asks about availability, pricing, or current offers from websites\n- Query asks about recent social media posts, trends, or viral content\n- Query asks about current job listings, real estate, or market data\n- Query asks about live events, schedules, or current availability\n\n❌ DOESN'T REQUIRE BROWSING (NO) when:\n- General knowledge questions about historical facts, definitions, concepts\n- Questions about well-established information that doesn't change\n- Creative writing, coding help, math problems, or educational content\n- Personal advice, opinions, or subjective questions\n- Questions about basic facts, scientific principles, or common knowledge\n- Simple greetings, thanks, or conversational responses\n- Questions that can be answered with existing AI knowledge\n- Questions about famous people's basic biographical information\n- Questions about established theories, concepts, or principles\n\nEXAMPLES:\n- \"What's the current price of Bitcoin?\" → YES|SEARCH|current Bitcoin price USD value today\n- \"What's happening in Ukraine today?\" → YES|SEARCH|Ukraine news today current events latest developments\n- \"Check the weather in New York\" → YES|SEARCH|New York weather today current forecast\n- \"What is machine learning?\" → NO (general knowledge)\n- \"Help me write a poem\" → NO (creative task)\n- \"What's 2+2?\" → NO (basic math)\n\nRESPONSE FORMAT:\nIf YES: \"YES|BROWSING_TYPE|refined_search_query\"\nIf NO: \"NO\"\n\nBROWSING_TYPE options:\n- SEARCH: For general web search queries\n- NAVIGATE: For specific website/URL content\n- EXTRACT: For extracting specific data from known sources\n\nSEARCH QUERY REFINEMENT RULES:\n- Remove conversational words like \"tell me\", \"what's\", \"please\", etc.\n- Focus on key terms and current/recent indicators\n- Include time-sensitive terms like \"today\", \"current\", \"latest\", \"recent\"\n- Make queries specific and search-engine friendly`;\n            const classificationPayload = {\n                model: 'gemini-2.0-flash-001',\n                messages: [\n                    {\n                        role: 'user',\n                        content: classificationPrompt\n                    }\n                ],\n                temperature: 0.1,\n                max_tokens: 150\n            };\n            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.classificationApiKey}`\n                },\n                body: JSON.stringify(classificationPayload),\n                signal: AbortSignal.timeout(3000) // Quick 3s timeout for classification\n            });\n            if (!response.ok) {\n                console.warn('[Browsing Detection] Classification failed, using fallback');\n                return this.fallbackDetection(query);\n            }\n            const result = await response.json();\n            const decision = result.choices?.[0]?.message?.content?.trim();\n            if (decision?.startsWith('YES|')) {\n                const parts = decision.substring(4).split('|');\n                const browsingType = parts[0]?.toLowerCase();\n                const refinedQuery = parts[1]?.trim();\n                console.log(`[Browsing Detection] Gemini decision for \"${query.substring(0, 50)}...\": YES (BROWSE) - Type: ${browsingType}, Query: \"${refinedQuery}\"`);\n                return {\n                    shouldBrowse: true,\n                    refinedQuery: refinedQuery || query,\n                    browsingType: browsingType || 'search',\n                    confidence: 0.9\n                };\n            } else if (decision?.startsWith('NO')) {\n                console.log(`[Browsing Detection] Gemini decision for \"${query.substring(0, 50)}...\": NO (SKIP) - AI can handle this`);\n                return {\n                    shouldBrowse: false,\n                    confidence: 0.9\n                };\n            } else {\n                // Fallback for unexpected response format\n                console.warn(`[Browsing Detection] Unexpected decision format: \"${decision}\", using fallback`);\n                return this.fallbackDetection(query);\n            }\n        } catch (error) {\n            console.warn('[Browsing Detection] Classification error, using fallback:', error);\n            return this.fallbackDetection(query);\n        }\n    }\n    /**\n   * Fallback detection logic when AI classification is unavailable\n   */ fallbackDetection(query) {\n        const lowerQuery = query.toLowerCase();\n        // Time-sensitive keywords\n        const timeKeywords = [\n            'current',\n            'today',\n            'now',\n            'latest',\n            'recent',\n            'live',\n            'real-time',\n            'breaking'\n        ];\n        const hasTimeKeywords = timeKeywords.some((keyword)=>lowerQuery.includes(keyword));\n        // Information type keywords\n        const infoKeywords = [\n            'news',\n            'price',\n            'weather',\n            'stock',\n            'rate',\n            'score',\n            'status',\n            'update'\n        ];\n        const hasInfoKeywords = infoKeywords.some((keyword)=>lowerQuery.includes(keyword));\n        // Question patterns that typically need browsing\n        const browsingPatterns = [\n            /what'?s happening/i,\n            /what'?s the (current|latest)/i,\n            /check the/i,\n            /find out/i,\n            /look up/i\n        ];\n        const matchesPattern = browsingPatterns.some((pattern)=>pattern.test(query));\n        const shouldBrowse = hasTimeKeywords || hasInfoKeywords || matchesPattern;\n        if (shouldBrowse) {\n            console.log(`[Browsing Detection] Fallback decision for \"${query.substring(0, 50)}...\": YES (BROWSE) - Pattern match`);\n            return {\n                shouldBrowse: true,\n                refinedQuery: query,\n                browsingType: 'search',\n                confidence: 0.6\n            };\n        } else {\n            console.log(`[Browsing Detection] Fallback decision for \"${query.substring(0, 50)}...\": NO (SKIP) - No browsing patterns`);\n            return {\n                shouldBrowse: false,\n                confidence: 0.6\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingDetectionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingDetectionService.ts\n");

/***/ })

};
;